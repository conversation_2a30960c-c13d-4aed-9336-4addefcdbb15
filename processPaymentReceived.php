<?php
include('includes/config.php');
$userid = -1;
if (isset($_COOKIE['userid']) && $_COOKIE['userid'] != "") {
    $userid = $_COOKIE['userid'];
}

include("includes/generalFunctions.class.php");
$general = new generalFunctions($connection);

// PayPal credentials live
$clientId       = PAYPALCLIENTID; 
$clientSecret   = PAYPALSECRET;
$paypalApi      = PAYPALAPIURL;

// Read raw JSON body
// $data = json_decode(file_get_contents('php://input'), true);

$orderId = mysqli_real_escape_string($connection, $_GET['orderID']);
if(empty($orderId)) {
    $_SESSION['msg'] = "Invalid order ID.";
    $_SESSION['heading'] = "Payment Error";
    $_SESSION['type'] = "error";
    echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
    exit;
}



function getAccessToken($clientId, $clientSecret, $paypalApi) {
    $url = "$paypalApi/v1/oauth2/token";
    $auth = base64_encode("$clientId:$clientSecret");

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . $auth,
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        error_log('cURL error: ' . curl_error($ch));
        curl_close($ch);
        return false;
    }

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode != 200) {
        error_log('Failed to get access token, HTTP code: ' . $httpCode);
        return false;
    }

    $data = json_decode($response, true);
    return $data['access_token'] ?? false;
}

function verifyOrder($orderId, $amount, $accessToken, $paypalApi) {
    
    $url = "$paypalApi/v2/checkout/orders/$orderId";

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        error_log('cURL error verifying order: ' . curl_error($ch));
        curl_close($ch);
        return false;
    }
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode != 200) {
        error_log("Verify Order failed: HTTP $httpCode\n$response");
        return false;
    }

    $order = json_decode($response, true);
    return $order;
}

$accessToken = getAccessToken($clientId, $clientSecret, $paypalApi);
if (!$accessToken) {
    $_SESSION['msg'] = "Payment has been failed.";
    $_SESSION['heading'] = "Payment Failed";
    $_SESSION['type'] = "error";
    echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
    exit;
    
}

$order = verifyOrder($orderId, 0, $accessToken, $paypalApi);
// print_r($order);
// exit;
if ($order && $order['status'] === 'COMPLETED')
{
    $encID = $order['purchase_units'][0]['reference_id'];
    $amount = $order['purchase_units'][0]['amount']['value'];

    $sql = "SELECT * FROM tempPament WHERE encID = '$encID' and `status` = 0";
    $result = mysqli_query($connection, $sql);
    if (mysqli_num_rows($result) > 0) {
        $tempPament = mysqli_fetch_assoc($result);
        if(floatval($amount) != floatval($tempPament['amount']))
        {
            $_SESSION['msg'] = "Internal Error Occured please contact support.";
            $_SESSION['heading'] = "Payment Error";
            $_SESSION['type'] = "error";
            echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
            exit;
        }
        $sql = "UPDATE tempPament SET status = 1 WHERE encID = '$encID'";
        mysqli_query($connection, $sql);

        if($tempPament['type'] <= 4) // single application
        {
            $id = $tempPament['visaID'];
            $row = $general->getVisaDetails($id);
            
            $TRN = $general->generateVisaFileNumber($id); // generate TRN
            $receivedAmount = floatval($order['purchase_units'][0]['amount']['value']);
            $transaction_id = $orderId;
            
            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                            VALUES 
                                            ('".$userid."', '$transaction_id', 'Visa Application Fee - {$TRN}', '".$receivedAmount."', 'credit', 'paypal', 1, '$id')";
            mysqli_query($connection, $sql);
            $payment_id = mysqli_insert_id($connection);
            if($row['type'] == 1)
            {
                $visaType = "Work Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 13,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
                $jobID = $row['jobID'];
                $general->minusOneJob($jobID);
            }
            elseif($row['type'] == 2)
            {
                $visaType = "Student Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
            }
            elseif($row['type'] == 3)
            {
                $visaType = "Visitor Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
            }
            else
            {
                $visaType = "Other Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
            }
            $subject = "Visa Application Received - Confirmation";
            $bodyMail = 'Dear '.$row['lastName'].',<br><br>We have successfully received your visa application. Our team is currently reviewing your submission, and we will update you on the next steps soon.<br><br>Application Details:<br><br>Applicant Name: <b>'.$row['lastName'].'</b><br>Visa Type: <b>'.$visaType.'</b><br>Application Number: <b>'.$TRN.'</b><br>Application Date: <b>'.$general->dateToRead($row['created']).'</b>';
            // $bodyMail add payment received receipt
            $bodyMail .= '<br><br>Payment Details:<br><br>Amount: <b>'.$receivedAmount.'</b><br>Transaction ID: <b>'.$transaction_id.'</b><br>Payment Date: <b>'.$general->dateToRead(date('Y-m-d H:i:s')).'</b>';
            $bodyMail .= '<br><br>If any additional information is required, we will reach out to you. For inquiries, feel free to contact our support <NAME_EMAIL>.<br><br>Thank you for choosing '.sitename;
            $general->sendEmail($row['lastName'], $row['email'], $subject, $bodyMail);


            if($row['type'] != 1)
            {
                // FCO Accounts Payment
                $feesAmount     = $general->getFeeByVisaApplicationID($id);
                $applicationFee = $feesAmount['applicationFee'];
                $fcoLevy        = $feesAmount['fcoLevy'];
                $commission     = $feesAmount['commission'];
                $webFee         = $feesAmount['webFee'];

                $sql = "SELECT * FROM `accounts`";
                $result = mysqli_query($connection, $sql);
                $row = mysqli_fetch_assoc($result);
                $applictionAccount  = $row['applicationFee'];
                $fcoLevyAccount     = $row['fcoLevyFee'];
                $commissionAccount  = $row['comissionFee'];
                $webFeeAccount      = $row['webFee'];
                
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                        VALUES 
                        ('-1', '0', 'Web fee (".$webFeeAccount.")', '".$webFee."', 'debit', 'paypal', 0, '$id')";
                mysqli_query($connection, $sql);
                
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                        VALUES 
                        ('-1', '0', 'FCO levy fee (".$fcoLevyAccount.")', '".$fcoLevy."', 'debit', 'paypal', 0, '$id')";
                mysqli_query($connection, $sql);
                
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                        VALUES 
                        ('-1', '0', 'Commission fee (".$commissionAccount.")', '".$commission."', 'debit', 'paypal', 0, '$id')";
                mysqli_query($connection, $sql);
            }
        }
        elseif($tempPament['type'] == 9) // multiple applications
        {
            
            foreach($_SESSION['multiApplication'] as $key => $value)
            {
                $id = $value;
                $row = $general->getVisaDetails($id);
                
                $TRN = $general->generateVisaFileNumber($id); // generate TRN
                $receivedAmount = (floatval($order['purchase_units'][0]['amount']['value'])/count($_SESSION['multiApplication']));
                $transaction_id = $orderId;
                
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                                VALUES 
                                                ('".$userid."', '$transaction_id', 'Visa Application Fee - {$TRN}', '".$receivedAmount."', 'credit', 'paypal', 1, '$id')";
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);
                if($row['type'] == 1)
                {
                    $visaType = "Work Visa";
                    $sql = "UPDATE `visaApplication` SET `status` = 13,TRN='$TRN' WHERE `id` = '$id'";
                    mysqli_query($connection, $sql);
                    $jobID = $row['jobID'];
                    $general->minusOneJob($jobID);
                }
                elseif($row['type'] == 2)
                {
                    $visaType = "Student Visa";
                    $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                    mysqli_query($connection, $sql);
                }
                else
                {
                    $visaType = "Visitor Visa";
                    $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                    mysqli_query($connection, $sql);
                }
                $subject = "Visa Application Received - Confirmation";
                $bodyMail = 'Dear '.$row['lastName'].',<br><br>We have successfully received your visa application. Our team is currently reviewing your submission, and we will update you on the next steps soon.<br><br>Application Details:<br><br>Applicant Name: <b>'.$row['lastName'].'</b><br>Visa Type: <b>'.$visaType.'</b><br>Application Number: <b>'.$TRN.'</b><br>Application Date: <b>'.$general->dateToRead($row['created']).'</b>';
                // $bodyMail add payment received receipt
                $bodyMail .= '<br><br>Payment Details:<br><br>Amount: <b>'.$receivedAmount.'</b><br>Transaction ID: <b>'.$transaction_id.'</b><br>Payment Date: <b>'.$general->dateToRead(date('Y-m-d H:i:s')).'</b>';
                $bodyMail .= '<br><br>If any additional information is required, we will reach out to you. For inquiries, feel free to contact our support <NAME_EMAIL>.<br><br>Thank you for choosing '.sitename;
                $general->sendEmail($row['lastName'], $row['email'], $subject, $bodyMail);


                if($row['type'] != 1)
                {
                    // FCO Accounts Payment
                    $feesAmount     = $general->getFeeByVisaApplicationID($id);
                    $applicationFee = $feesAmount['applicationFee'];
                    $fcoLevy        = $feesAmount['fcoLevy'];
                    $commission     = $feesAmount['commission'];
                    $webFee         = $feesAmount['webFee'];

                    $sql = "SELECT * FROM `accounts`";
                    $result = mysqli_query($connection, $sql);
                    $row = mysqli_fetch_assoc($result);
                    $applictionAccount  = $row['applicationFee'];
                    $fcoLevyAccount     = $row['fcoLevyFee'];
                    $commissionAccount  = $row['comissionFee'];
                    $webFeeAccount      = $row['webFee'];
                    
                    $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                            VALUES 
                            ('-1', '0', 'Web fee (".$webFeeAccount.")', '".$webFee."', 'debit', 'paypal', 0, '$id')";
                    mysqli_query($connection, $sql);
                    
                    $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                            VALUES 
                            ('-1', '0', 'FCO levy fee (".$fcoLevyAccount.")', '".$fcoLevy."', 'debit', 'paypal', 0, '$id')";
                    mysqli_query($connection, $sql);
                    
                    $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                            VALUES 
                            ('-1', '0', 'Commission fee (".$commissionAccount.")', '".$commission."', 'debit', 'paypal', 0, '$id')";
                    mysqli_query($connection, $sql);
                }
            }
            unset($_SESSION['multiApplication']);
        
        }
        elseif($tempPament['type'] == 5) // eoi payment
        {
            $id = $tempPament['visaID'];
            $receivedAmount = floatval($order['purchase_units'][0]['amount']['value']);
            $transaction_id = $orderId;
            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                            VALUES 
                                            ('$id', '$transaction_id', 'Become an Agent Fee', '".$receivedAmount."', 'credit', 'paypal', 1, '$id')";
            mysqli_query($connection, $sql);
            $payment_id = mysqli_insert_id($connection);
            


            $agentRegistrationsFee = $general->getAgentRegistrationsFee();
            $transferTo = $agentRegistrationsFee['transfer'];
            $transferAmount = $agentRegistrationsFee['amount'];

            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                    VALUES 
                    ('-1', '0', 'Become an Agent Fee (".$transferTo.")', '".$transferAmount."', 'debit', 'paypal', 0, '$id')";
            mysqli_query($connection, $sql);
            
            // update `expression_of_interest` status to 1
            $sql = "UPDATE `expression_of_interest` SET `status` = 1 WHERE `id` = '$id'";
            mysqli_query($connection, $sql);
            $eoiData = $general->getEOIDataByID($id);
            $subject = "Agent Application Received - Confirmation";
            $bodyMail = 'Dear '.$eoiData['full_name'].',<br><br>We have successfully received your visa application. Our team is currently reviewing your submission, and we will update you on the next steps soon.<br><br>Application Details:<br><br>Applicant Name: <b>'.$eoiData['full_name'].'</b><br>Application Date: <b>'.$general->dateToRead($eoiData['created']).'</b>';
            // $bodyMail add payment received receipt
            $notification = array(
                'uid' => '-1',
                'title' => "New Agent Application Received.</b>",
                'page' => 'agentRequests.php',
                'status' => 0
            );
            $general->insertData('notification', $notification);
            $bodyMail .= '<br><br>Payment Details:<br><br>Amount: <b>'.$receivedAmount.'</b><br>Transaction ID: <b>'.$transaction_id.'</b><br>Payment Date: <b>'.$general->dateToRead(date('Y-m-d H:i:s')).'</b>';
            $bodyMail .= '<br><br>If any additional information is required, we will reach out to you. For inquiries, feel free to contact our support <NAME_EMAIL>.<br><br>Thank you for choosing '.sitename;
            
            $general->sendEmail($eoiData['full_name'], $eoiData['email_address'], $subject, $bodyMail);

        }
        elseif($tempPament['type'] == 6) // agent fee
        {
            $id = $tempPament['visaID'];
            $receivedAmount = floatval($order['purchase_units'][0]['amount']['value']);
            $transaction_id = $orderId;

            $query = "SELECT * FROM `expression_of_interest` WHERE id = '$id' and `status`=2";
            $result = mysqli_query($connection, $query);
            if(mysqli_num_rows($result) == 0)
            {
                $_SESSION['msg'] = "Some internal error occured please try again.";
                $_SESSION['heading'] = "Invalid Request";
                $_SESSION['type'] = "error";
                echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
                exit;
            }
            $row = mysqli_fetch_array($result);
            
            $name = $row['full_name'];
            $lname = $row['full_name'];
            $email = $row['email_address'];
            $mobile = $row['contact_number'];
            $country = $row['nationality'];
            $industry = $row['current_occupation'];
            $contactPerson = $row['full_name'];
            $agentPackage = $row['agentPackage'];

            $username = substr($name, 0, 1) . substr($lname, 0, 1) . rand(1000, 9999);
            $username = strtolower($username);
            $strSQL = mysqli_query($connection, "SELECT `id` from `users` where `username`='" . $username . "'");
            if (mysqli_num_rows($strSQL) > 0) 
            {
                $username = substr($name, 0, 1) . substr($lname, 0, 1) . rand(10000, 99999);
                $username = strtolower($username);
            }
            $rawPassword = substr(str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, 8);
            $password = password_hash($rawPassword, PASSWORD_DEFAULT);

            $query = "INSERT INTO `users`(`name`, `lname`, `email`, `phone`, `country`, `password`, `industry`, `contactPerson`, `website`, `type`,`status`,`agentPackage`,`username`) 
            VALUES ('$name', '$lname', '$email', '$mobile', '$country', '$password', '$industry', '$contactPerson', '', '3', '1', '$agentPackage', '$username')";
            mysqli_query($connection, $query);
            $userid = mysqli_insert_id($connection);

            // update `expression_of_interest` 
            $sql = "UPDATE `expression_of_interest` SET `status` = 4,`uid`='$userid' WHERE `id` = '$id'";
            mysqli_query($connection, $sql);

            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                            VALUES 
                                            ('".$userid."', '$transaction_id', 'Agent Account Fee', '".$receivedAmount."', 'credit', 'paypal', 1, '$id')";
            mysqli_query($connection, $sql);

            // share funds to FCO accounts

            $agentFullDetails = $general->getAgentFullDetailsByID($packageID);

            $user1 = $agentFullDetails['user1'];
            $amount1 = $agentFullDetails['amount1'];

            $user2 = $agentFullDetails['user2'];
            $amount2 = $agentFullDetails['amount2'];

            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                    VALUES 
                    ('-1', '0', 'Agent Account Fee (".$user1.")', '".$amount1."', 'debit', 'paypal', 0, '$id')";

            mysqli_query($connection, $sql);
            
            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                    VALUES 
                    ('-1', '0', 'Agent Account Fee (".$user2.")', '".$amount2."', 'debit', 'paypal', 0, '$id')";

            mysqli_query($connection, $sql);
            

            $subject = "Your Account Has Been Successfully Created";
            $bodyMail = 'Dear '.$row['full_name'].',<br><br>We are excited to inform you that your account has been successfully created. Below are your login credentials:<br><br>Login Details:<br><br>User ID: <b>'.$email.'</b><br>Password: <b>'.$rawPassword.'</b><br><br>You can log in to your account using the following link:<br><a href="'.siteurl.'/login.php">'.siteurl.'/login.php</a><br><br>Next Steps:<br><br><ol><li>Log in using your credentials.</li><li>Explore your dashboard and available features.</li><li>Feel free to update your password for enhanced security.</li></ol><br><br>If you have any questions or face any issues accessing your account, please contact <NAME_EMAIL>.<br><br>Welcome to '.sitename.'! We are excited to have you on board.';

            $general->sendEmail($row['full_name'], $row['email_address'], $subject, $bodyMail);
        }
        elseif($tempPament['type'] == 7) // additional fee
        {
            $id = $tempPament['visaID'];
            $receivedAmount = floatval($order['purchase_units'][0]['amount']['value']);
            $transaction_id = $orderId;

            $sql = "SELECT * FROM `addtionalFee` WHERE `id` = '$id' and `status`=0";
            $result = mysqli_query($connection, $sql);
            if(mysqli_num_rows($result) == 0)
            {
                $_SESSION['msg'] = "Some internal error occured please try again.";
                $_SESSION['heading'] = "Invalid Request";
                $_SESSION['type'] = "error";
                echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
                exit;
            }
            $row = mysqli_fetch_array($result);
            $title = $row['title'];
            $visaApplication = $general->getVisaDetails($row['visa_id']);
            $userAccount = $general->getUserByID($visaApplication['uid']);
            $addtionalID = $row['id'];
            
            
            
            $sql = "UPDATE `addtionalFee` SET `status` = 1 WHERE `encID` = '$id'";
            mysqli_query($connection, $sql);

            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                            VALUES 
                                            ('".$visaApplication['uid']."', '$transaction_id', 'Addtional fee ($title)', '".$receivedAmount."', 'credit', 'paypal', 1, '$addtionalID')";
            mysqli_query($connection, $sql);

            // share funds to FCO accounts

            
            $email1 = $row['email1'];
            $amount1 = $row['amount1'];
            $email2 = $row['email2'];
            $amount2 = $row['amount2'];
            $email3 = $row['email3'];
            $amount3 = $row['amount3'];

            if($email1 !="" && $amount1 != "")
            {
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                        VALUES 
                        ('-1', '0', 'Addtional fee (".$email1.")', '".$amount1."', 'debit', 'paypal', 0, '$addtionalID')";
                
                mysqli_query($connection, $sql);
            }
            if($email1 !="" && $amount1 != "")
            {
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                        VALUES 
                        ('-1', '0', 'Addtional fee (".$email2.")', '".$amount2."', 'debit', 'paypal', 0, '$addtionalID')";
                
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);
            }
            if($email1 !="" && $amount1 != "")
            {
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                        VALUES 
                        ('-1', '0', 'Addtional fee (".$email3.")', '".$amount3."', 'debit', 'paypal', 0, '$addtionalID')";
                
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);
            }
            
            $subject = "Addtional Fee Payment Received";
            $bodyMail = 'Dear '.$userAccount['name']." ".$userAccount['lname'].',<br><br>We are excited to inform you that your payment has been successfully received. Below are the details:<br><br>Payment Details:<br><br>Amount: <b>'.$receivedAmount.'</b><br><br>If you have any questions or face any issues accessing your account, please contact <NAME_EMAIL>.<br><br>Thank you for your payment.';
            
            $general->sendEmail($userAccount['name']." ".$userAccount['lname'], $userAccount['email'], $subject, $bodyMail);
            $notification = array(
                'uid' => '-1',
                'title' => "Addtional Visa Fee Received TRN: [{$visaApplication['TRN']}].</b>",
                'page' => 'visaApplications.php',
                'status' => 0
            );
            $general->insertData('notification', $notification);

        }
        elseif($tempPament['type'] == 8) // job remaining fee
        {
            $id = $tempPament['visaID'];
            $receivedAmount = floatval($order['purchase_units'][0]['amount']['value']);
            $transaction_id = $orderId;

            $id = $tempPament['visaID'];
            $row = $general->getVisaDetails($id);
            
            $TRN = $row['TRN'];
            $receivedAmount = floatval($order['purchase_units'][0]['amount']['value']);
            $transaction_id = $orderId;
            
            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                            VALUES 
                                            ('".$userid."', '$transaction_id', 'Visa Application Fee - {$TRN}', '".$receivedAmount."', 'credit', 'paypal', 1, '$id')";
            mysqli_query($connection, $sql);
            $payment_id = mysqli_insert_id($connection);
            if($row['type'] == 1)
            {
                $visaType = "Work Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 13 WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
                $jobID = $row['jobID'];
                $general->minusOneJob($jobID);
            }
            elseif($row['type'] == 2)
            {
                $visaType = "Student Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
            }
            elseif($row['type'] == 3)
            {
                $visaType = "Visitor Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
            }
            else
            {
                $visaType = "Other Visa";
                $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
            }

            $subject = "Visa Application Received - Confirmation";
            $bodyMail = 'Dear '.$row['lastName'].',<br><br>We have successfully received your visa application. Our team is currently reviewing your submission, and we will update you on the next steps soon.<br><br>Application Details:<br><br>Applicant Name: <b>'.$row['lastName'].'</b><br>Visa Type: <b>'.$visaType.'</b><br>Application Number: <b>'.$TRN.'</b><br>Application Date: <b>'.$general->dateToRead($row['created']).'</b>';
            // $bodyMail add payment received receipt
            $bodyMail .= '<br><br>Payment Details:<br><br>Amount: <b>'.$receivedAmount.'</b><br>Transaction ID: <b>'.$transaction_id.'</b><br>Payment Date: <b>'.$general->dateToRead(date('Y-m-d H:i:s')).'</b>';
            $bodyMail .= '<br><br>If any additional information is required, we will reach out to you. For inquiries, feel free to contact our support <NAME_EMAIL>.<br><br>Thank you for choosing '.sitename;
            $general->sendEmail($row['lastName'], $row['email'], $subject, $bodyMail);


            
            // FCO Accounts Payment
            $feesAmount     = $general->getFeeByVisaApplicationID($id);
            $applicationFee = $feesAmount['applicationFee'];
            $fcoLevy        = $feesAmount['fcoLevy'];
            $commission     = $feesAmount['commission'];
            $webFee         = $feesAmount['webFee'];

            $sql = "SELECT * FROM `accounts`";
            $result = mysqli_query($connection, $sql);
            $row = mysqli_fetch_assoc($result);
            $applictionAccount  = $row['applicationFee'];
            $fcoLevyAccount     = $row['fcoLevyFee'];
            $commissionAccount  = $row['comissionFee'];
            $webFeeAccount      = $row['webFee'];
            
            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                    VALUES 
                    ('-1', '0', 'Web fee (".$webFeeAccount.")', '".$webFee."', 'debit', 'paypal', 0, '$id')";
            mysqli_query($connection, $sql);
            
            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                    VALUES 
                    ('-1', '0', 'FCO levy fee (".$fcoLevyAccount.")', '".$fcoLevy."', 'debit', 'paypal', 0, '$id')";
            mysqli_query($connection, $sql);
            
            $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                    VALUES 
                    ('-1', '0', 'Commission fee (".$commissionAccount.")', '".$commission."', 'debit', 'paypal', 0, '$id')";
            mysqli_query($connection, $sql);

            $subject = "Complete Work Visa Fee Payment Received";
            $bodyMail = 'Dear '.$userAccount['name']." ".$userAccount['lname'].',<br><br>We are excited to inform you that your payment has been successfully received. Below are the details:<br><br>Payment Details:<br><br>Amount: <b>'.$receivedAmount.'</b><br><br>If you have any questions or face any issues accessing your account, please contact <NAME_EMAIL>.<br><br>Thank you for your payment.';
            
            $general->sendEmail($userAccount['name']." ".$userAccount['lname'], $userAccount['email'], $subject, $bodyMail);
            $notification = array(
                'uid' => '-1',
                'title' => "Complete Work Visa Fee Received TRN: [{$TRN}].</b>",
                'page' => 'visaApplications.php',
                'status' => 0
            );
            $general->insertData('notification', $notification);

        }
        $_SESSION['msg'] = "Your request has been submitted We shall contact you via email or phone.";
        $_SESSION['heading'] = "Payment Received";
        $_SESSION['type'] = "success";
        echo "<meta http-equiv='refresh' content='0;url=notify.php'>";

    } 
    else
    {
        $_SESSION['msg'] = "Payment already processed or invalid order ID.";
        $_SESSION['heading'] = "Payment Error";
        $_SESSION['type'] = "error";
        echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
        exit;
    }
}
elseif($order && $order['status'] === 'PENDING')
{
    $_SESSION['msg'] = "Payment is pending.";
    $_SESSION['heading'] = "Payment Pending";
    $_SESSION['type'] = "warning"; 
    echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
    exit;
}
else 
{
    $_SESSION['msg'] = "Payment has been failed.";
    $_SESSION['heading'] = "Payment Failed";
    $_SESSION['type'] = "error";
    echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
    exit;
}
?>