<?php

header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');
include_once('includes/config.php');
include("includes/generalFunctions.class.php");
// require '../vendor/autoload.php';

$general = new generalFunctions($connection);

// post requests
if (isset($_POST['action'])) {
    if ($_POST['action'] == "checkOnArival") {
        $__data = array();
        $countryFrom    = mysqli_real_escape_string($connection, $_POST['countryFrom']);
        $countryTo      = mysqli_real_escape_string($connection, $_POST['countryTo']);

        $query = "SELECT * FROM `visaOnArrival` WHERE `countryFrom`='$countryFrom' and `countryTo`='$countryTo'";
        $result = mysqli_query($connection, $query);
        if (mysqli_num_rows($result) > 0) {
            $__data['message'] = "visaOnArrival";
        } else {
            $__data['message'] = "visaRequired";
        }

        echo json_encode($__data);
        exit;
    }
    if ($_POST['action'] == "checkEmail") {
        $__data = array();
        $email = mysqli_real_escape_string($connection, $_POST['email']);
        $query = "SELECT * FROM `users` WHERE `email`='$email'";
        $result = mysqli_query($connection, $query);
        if (mysqli_num_rows($result) > 0) {
            $__data['status'] = "error";
            $__data['message'] = "Email already exists";
        }

        echo json_encode($__data);
        exit;
    }
}
// get requests
if (isset($_GET['action'])) {

    if ($_GET['action'] == "getApplicationPayment") {
        $encID = mysqli_real_escape_string($connection, $_GET['encID']);
        $paymentID = mysqli_real_escape_string($connection, $_GET['paymentID']);
        $query = "SELECT * FROM tempPament WHERE id = '$paymentID' AND encID = '$encID'";
        $result = mysqli_query($connection, $query);

        if (mysqli_num_rows($result) > 0) {
            $tempPament = mysqli_fetch_assoc($result);
            echo json_encode([
                'success' => true,
                'amount' => $tempPament['amount'],
                'order_id' => $tempPament['encID'],
                'currency' => 'USD',
            ]);
        }
    }

    if ($_GET['action'] == "universityInCountry") {
        $country_id        = mysqli_real_escape_string($connection, $_GET['country_id']);
        $query = "SELECT * FROM `university` WHERE `country`='$country_id'";
        $result = mysqli_query($connection, $query);
        while ($rows = mysqli_fetch_array($result)) {
            $__data['universities'][] = $rows;
        }


        echo json_encode($__data);

        exit;
    }
    if ($_GET['action'] == "getProfile") {
        $id = mysqli_real_escape_string($connection, $_GET['id']);
        $query = "SELECT * FROM `portfolio` WHERE `id`='$id'";
        $result = mysqli_query($connection, $query);
        $rows = mysqli_fetch_array($result);


        ?>
        <div class="container">
            <div class="portfolio-card">
                <div class="row">
                    <div class="col-md-6 col-sm-6 col-6">
                        <img src="uploads/<?php echo str_replace(".png", "_1.png", $rows['picture']); ?>" alt="<?php echo $rows['name']; ?>" class="profile-img" />
                    </div>
                    <div class="col-md-6 col-sm-6 col-6 d-flex flex-column text-start">
                        <!-- logo -->
                        <div class="text-end"><img src="./assets/images/logo/logo_black.svg" alt="" style="width:200px;"></div>
                        <h3 class="mt-3 text-primary"><?php echo $rows['name']; ?></h3>
                        <div class="divUserInformation">
                            <?php echo $rows['description']; ?>
                        </div>
                        <p class="warning-text mt-auto">⚠️ Warning Against the Unauthorized Use of Political Leaders' Images for unethical, defamatory, or misleading purposes may lead to criminal prosecution and severe penalties under various international and national laws.</p>
                    </div>
                </div>

            </div>
        </div>
        <?php
        exit;
    }
}

// GET requests
if (isset($_GET['action'])) {
    if ($_GET['action'] == "getAgentInfo") {
        $id = mysqli_real_escape_string($connection, $_GET['id']);
        $query = "SELECT eoi.*, u.status as user_status, u.email as user_email FROM `expression_of_interest` eoi
                  LEFT JOIN `users` u ON eoi.uid = u.id
                  WHERE eoi.id = " . $id;
        $result = mysqli_query($connection, $query);

        if (mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);

            // Determine status
            if ($row['user_status'] == 1) {
                $status = "Registered (Active)";
                $statusClass = "success";
            } elseif ($row['user_status'] == 2) {
                $status = "Inactive";
                $statusClass = "warning";
            } else {
                $status = "Suspended";
                $statusClass = "danger";
            }
            // registrationStart is last visa application agent processed
            $registrationStart = $general->getLastVisaApplicationDate($row['uid']);

            // Calculate registration dates (using available data or defaults)
            // $registrationStart = $row['submission_date'] ? date('d F Y', strtotime($row['submission_date'])) : '01 December 2024';
            $licenseExpiry = $row['l_expiry'] ? $general->dateToRead($row['l_expiry']) : '01 December 2027';

            // Get agent photo
            $agentPhoto = '';
            if (!empty($row['passport_photo']) && file_exists('uploads/' . $row['passport_photo'])) {
                $agentPhoto = 'uploads/' . $row['passport_photo'];
            } else {
                $agentPhoto = 'assets/user.png'; // Default user image
            }

        ?>
            <div class="row">
                <div class="col-md-4 text-center mb-3">
                    <div class="agent-photo-container">
                        <img src="<?php echo $agentPhoto; ?>" alt="<?php echo htmlspecialchars($row['full_name']); ?>"
                            class="img-fluid rounded-circle border border-3 border-primary"
                            style="width: 150px; height: 150px; object-fit: cover;">
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="agent-details">
                        <h4 class="text-primary mb-3">
                            <i class="bi bi-person-badge"></i>
                            <?php echo htmlspecialchars($row['full_name']); ?>
                        </h4>

                        <div class="info-item mb-2">
                            <strong><i class="bi bi-card-text"></i> Migration agent registration number:</strong>
                            <span class="ms-2"><?php echo htmlspecialchars($row['license_num']); ?></span>
                        </div>

                        <div class="info-item mb-2">
                            <strong><i class="bi bi-check-circle"></i> Current status:</strong>
                            <span class="badge bg-<?php echo $statusClass; ?> ms-2 text-white"><?php echo $status; ?></span>
                        </div>

                        <div class="info-item mb-2">
                            <strong><i class="bi bi-calendar-check"></i> Agent most recent registration commenced on:</strong>
                            <span class="ms-2"><?php echo $registrationStart; ?></span>
                        </div>

                        <div class="info-item mb-2">
                            <strong><i class="bi bi-calendar-x"></i> Valid till:</strong>
                            <span class="ms-2"><?php echo $licenseExpiry; ?></span>
                        </div>

                        <div class="info-item mb-2">
                            <strong><i class="bi bi-envelope"></i> Email address:</strong>
                            <span class="ms-2">
                                <?php
                                $email = !empty($row['user_email']) ? $row['user_email'] : $row['email_address'];
                                echo htmlspecialchars($email);
                                ?>
                            </span>
                        </div>

                        <!-- <?php if (!empty($row['contact_number'])): ?>
                            <div class="info-item mb-2">
                                <strong><i class="bi bi-telephone"></i> Contact Number:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($row['contact_number']); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($row['nationality'])): ?>
                            <div class="info-item mb-2">
                                <strong><i class="bi bi-globe"></i> Nationality:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($general->getCountryNameByiso2($row['nationality'])); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($row['current_occupation'])): ?>
                            <div class="info-item mb-2">
                                <strong><i class="bi bi-briefcase"></i> Current Occupation:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($row['current_occupation']); ?></span>
                            </div>
                        <?php endif; ?> -->
                    </div>
                </div>
            </div>

            <style>
                .info-item {
                    padding: 8px 0;
                    border-bottom: 1px solid #f0f0f0;
                }

                .info-item:last-child {
                    border-bottom: none;
                }

                .agent-details {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 4px solid #0d6efd;
                }
            </style>
        <?php
        } else {
        ?>
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i>
                Agent information not found.
            </div>
        <?php
        }
        exit;
    }
}
