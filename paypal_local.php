<?php
require 'vendor/autoload.php';

use PayPal\Rest\ApiContext;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Api\Payout;
use PayPal\Api\PayoutSenderBatchHeader;
use PayPal\Api\PayoutItem;

// live
$clientId = PAYPALCLIENTID; 
$clientSecret = PAYPALSECRET;


// Set up the API context with credentials
$apiContext = new ApiContext(
    new OAuthTokenCredential(
        $clientId,
        $clientSecret
    )
);

// Configure the SDK for sandbox or live mode
$apiContext->setConfig(
    array(
        'mode' => 'sandbox', // or 'live' for production
        'log.LogEnabled' => true,
        'log.FileName' => 'PayPal.log',
        'log.LogLevel' => 'DEBUG', // Debug mode
    )
);

function payoutToFCOAccounts($receiver,$amount,$note)
{
    global $apiContext;

    

    $payouts = new Payout();

    $senderBatchHeader = new PayoutSenderBatchHeader();
    $senderBatchHeader->setSenderBatchId(uniqid())
        ->setEmailSubject($note);

    $payoutItem = new PayoutItem([
        'recipient_type' => 'EMAIL',
        'receiver' => $receiver, // Recipient's email address
        'amount' => [
            'value' => $amount, // Amount to send
            'currency' => 'USD' // Currency code
        ],
        'note' => $note,
        'sender_item_id' => uniqid()
    ]);

    $payouts->setSenderBatchHeader($senderBatchHeader)
        ->addItem($payoutItem);

    try {
        $output = $payouts->create([], $apiContext);
        // echo "Payout created successfully. Batch ID: " . $output->getBatchHeader()->getPayoutBatchId();
        return $output->getBatchHeader()->getPayoutBatchId();
    } catch (Exception $ex) {
        echo "Error: " . $ex->getMessage();
    }
}

?>