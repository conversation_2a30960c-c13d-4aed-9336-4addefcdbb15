<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');
include_once('../includes/config.php');
include_once('includes/session.php');
include("../includes/generalFunctions.class.php");
// require '../vendor/autoload.php';

$general = new generalFunctions($connection);

// post requests
if (isset($_POST['action'])) {
    if($_POST['action'] == "updatePaypalPayments")
    {
        $value = mysqli_real_escape_string($connection, $_POST['value']);
        mysqli_query($connection, "UPDATE `settings` SET `value`='$value' WHERE `nameSlug`='paypal_payments'");
        exit;
    }
    if($_POST['action'] == "getLicense")
    {
        $id = mysqli_real_escape_string($connection, $_POST['id']);
        $license = $general->getLicense($id);
        echo json_encode($license);
        exit;
    }
}
// get requests
if (isset($_GET['action'])) {
    if ($_GET['action'] == "universityInCountry") {
        $country_id        = mysqli_real_escape_string($connection, $_GET['country_id']);
        $query = "SELECT * FROM `university` WHERE `country`='$country_id'";
        $result = mysqli_query($connection, $query);
        while ($rows = mysqli_fetch_array($result)) {
            $__data['universities'][] = $rows;
        }
        echo json_encode($__data);
        exit;
    }

    if ($_GET['action'] == "viewVisaApplication") {
        $id        = mysqli_real_escape_string($connection, $_GET['id']);
        $query = "SELECT * FROM `visaApplication` WHERE `id`=" . $id;
        $sqlQuery = mysqli_query($connection, $query);
        $rows = mysqli_fetch_assoc($sqlQuery);
        // SELECT `id`, `full_name`, `date_of_birth`, `nationality`, `contact_number`, `email_address`, `current_address`, `passport_number`, `passport_expiry_date`, `current_occupation`, `previous_experience`, `passport_copy`, `passport_photo`, `certificates`, `declaration`, `signature`, `submission_date`, `status`, `updated`, `created` FROM `expression_of_interest` WHERE 1
        if($rows['photo'] == '')
            $rows['photo'] = 'ph.jpg';
        ?>
        <div class="mb-3 bg-gray-100 p-3">
            <div class="form-group mb-4">
                <!-- applyingFor -->
                <label for="applyingFor" class="form-label fs-4 fw-bolder">Select the country or territory you are applying for</label>
                <p class="lead" id="applyingFor_r">
                    <?php echo $general->getCountryNameByiso2($rows['applyingFor']); ?>
                </p>
            </div>
            <?php if ($rows['university'] != '') { ?>
                <div class="form-group mb-4">
                    <label for="university" class="form-label fs-4 fw-bolder">University</label>
                    <p class="lead" id="university">
                        <?php echo $rows['university']; ?>
                    </p>
                </div>
            <?php } ?>
            <?php if ($rows['course'] != '') { ?>
                <div class="form-group mb-4">
                    <label for="course" class="form-label fs-4 fw-bolder">Course</label>
                    <p class="lead" id="course">
                        <?php echo $rows['course']; ?>
                    </p>
                </div>
            <?php } ?>
            <div class="form-group mb-4">
                <label for="countryFrom" class="form-label fs-4 fw-bolder">Select your nationality as shown on the passport you will be travelling on</label>
                <p class="lead" id="nationality_r">
                    <?php echo $general->getCountryNameByiso2($rows['nationality']); ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="countryFrom" class="form-label fs-4 fw-bolder">Select the issuing country or territory as shown on your passport</label>
                <p class="lead" id="country_r">
                    <?php echo $general->getCountryNameByiso2($rows['country']); ?>
                </p>
            </div>

            <div class="form-group mb-4">
                <label for="passportNumber" class="form-label fs-4 fw-bolder">Enter your passport number exactly as it appears on your passport</label>
                <p class="lead" id="passportNumber_r">
                    <?php echo $rows['passportNumber']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="passportExpiry" class="form-label fs-4 fw-bolder">Select the expiry date as shown on your passport</label>
                <p class="lead" id="expiry_r">
                    <?php echo $rows['expiryDate'] . "/" . $rows['expiryMonth'] . "/" . $rows['expiryYear']; ?>
                </p>
            </div>
            <!-- download passport image, resume if exist, offer letter if exist -->
            <div class="form-group mb-4">
                <label for="passport" class="form-label fs-4 fw-bolder">Copy of the personal details page of passport</label>
                <p class="lead" id="passport"><a href="../uploads/<?php echo $rows['passport']; ?>" download>Download</a></p>
            </div>


        </div>

        <div class="mb-3 bg-gray-100 p-3">
            <h3 class="mb-4">Traveller details</h3>
            <hr>
            <div class="form-group mb-4">
                <img id="image-preview_r" src="../uploads/<?php echo $rows['photo'] ?>" alt="Image Preview" width="200px">
            </div>
            <div class="form-group mb-4">
                <label for="lastName" class="form-label fs-4 fw-bolder">Enter your family/last name(s) as shown on your passport</label>
                <p class="lead" id="lastName_r">
                    <?php echo $rows['lastName']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="middleName" class="form-label fs-4 fw-bolder">Enter your given name(s) including your middle name(s) as shown on your passport</label>
                <p class="lead" id="middleName_r">
                    <?php echo $rows['middleName']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="differentName" class="form-label fs-4 fw-bolder">Have you ever been known by a different name?</label>
                <p class="lead" id="differentName_r">
                    <?php echo $rows['differentName']; ?>
                </p>
            </div>
            <?php if ($rows['differentName'] == "Yes") { ?>

                <div class="row otherNamesDiv mb-4">
                    <div class="col-6">
                        <div class="form-group mb-4">
                            <label for="passportNumber" class="form-label fs-4 fw-bolder">Other family/last name</label>
                            <p class="lead" id="otherFamily_r">
                                <?php echo $rows['otherFamily']; ?>
                            </p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group mb-4">
                            <label for="passportNumber" class="form-label fs-4 fw-bolder">Other given name(s)</label>
                            <p class="lead" id="otherGiven_r">
                                <?php echo $rows['otherGiven']; ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="form-group mb-4">
                <label for="differentName" class="form-label fs-4 fw-bolder ">Select your gender as shown on your passport</label>
                <p class="lead" id="gender_r">
                    <?php echo $rows['gender']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="passportExpiry" class="form-label fs-4 fw-bolder">Select your date of birth as shown on your passport</label>
                <p class="lead" id="dobDate_r">
                    <?php echo $rows['dobDate'] . "/" . $rows['dobMonth'] . "/" . $rows['dobYear']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="birthplace" class="form-label fs-4 fw-bolder">Enter your place of birth</label>
                <p class="lead" id="birthplace_r">
                    <?php echo $rows['birthplace']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="birthCountry" class="form-label fs-4 fw-bolder">Select your country or territory of birth</label>
                <p class="lead" id="birthCountry_r">
                    <?php echo $general->getCountryNameByiso2($rows['birthCountry']); ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="nationalID" class="form-label fs-4 fw-bolder">Enter your National Identity Number, if you have one</label>
                <p class="lead" id="nationalID_r">
                    <?php echo $rows['nationalID']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="email" class="form-label fs-4 fw-bolder">Enter a valid email address</label>
                <p class="lead" id="email_r">
                    <?php echo $rows['email']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="email" class="form-label fs-4 fw-bolder">Contact Number</label>
                <p class="lead" id="contactNumber_r">
                    <?php echo $rows['contactNumber']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="poc" class="form-label fs-4 fw-bolder">Point in Contact</label>
                <p class="lead" id="poc">
                    <?php 
                    if($rows['poc'] == "Other"){
                        echo $rows['poc_other'];
                    }else{
                        echo $rows['poc'];
                    }
                    ?>
                </p>
            </div>
            <?php if ($rows['about'] != '') { ?>
                <div class="form-group mb-4">
                    <label for="about" class="form-label fs-4 fw-bolder">About</label>
                    <p class="lead" id="about">
                        <?php echo $rows['about']; ?>
                    </p>
                </div>
            <?php } ?>

            <?php if ($rows['resume'] != '') { ?>
                <div class="form-group mb-4">
                    <label for="resume" class="form-label fs-4 fw-bolder">Resume</label>
                    <p class="lead" id="resume"><a href="../uploads/resumes/<?php echo $rows['resume']; ?>" download>Download</a></p>
                </div>
            <?php } ?>
            <?php if ($rows['offer'] != '') { ?>
                <div class="form-group mb-4">
                    <label for="offer" class="form-label fs-4 fw-bolder">Offer Letter</label>
                    <p class="lead" id="offer"><a href="../uploads/<?php echo $rows['offer']; ?>" download>Download</a></p>
                </div>
            <?php } ?>

        </div>
        
        <div class="mb-3 bg-gray-100 p-3">
            <h3 class="mb-4">Eligibility questions</h3>
            <hr>
            <div class="form-group mb-4">
                <label for="deport" class="form-label fs-4 fw-bolder">Have you ever been deported, removed or excluded from any country?</label>
                <p class="lead" id="deport_r">
                    <?php echo $rows['deport']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="prohibited" class="form-label fs-4 fw-bolder">Are you currently prohibited from entering any country in the past?</label>
                <p class="lead" id="prohibited_r">
                    <?php echo $rows['prohibited']; ?>
                </p>
            </div>
            <div class="form-group mb-4">
                <label for="convicted" class="form-label fs-4 fw-bolder">Have you ever been convicted of any offence (in any country)?</label>
                <p class="lead" id="convicted_r">
                    <?php echo $rows['convicted']; ?>
                </p>
            </div>
            <?php if ($rows['convicted'] == "Yes") { ?>
                <div class="row convictedDiv mb-4">
                    <div class="form-group mb-4">
                        <label for="passportNumber" class="form-label fs-4 fw-bolder">Have you ever been convicted of an offence for which you were sentenced to five years or more imprisonment?</label>
                        <p class="lead" id="imprisonment_r"></p>
                    </div>
                    <div class="form-group mb-4">
                        <label for="passportNumber" class="form-label fs-4 fw-bolder">In the last 10 years have you been convicted of an offence for which you were sentenced to a prison term of 12 months or more?</label>
                        <p class="lead" id="offence_r"></p>
                    </div>
                </div>
            <?php } ?>
        </div>
        <div class="mb-3 bg-gray-100 p-3">
            <h3 class="mb-4">Additional Details</h3>
            <?php
            $query = "SELECT * FROM `visaApplicationAddInfo` WHERE `visaID`=" . $id;
            // echo $query;
            $sqlQuery = mysqli_query($connection, $query);
            while($rows = mysqli_fetch_assoc($sqlQuery)){
                if(userid == $rows['uid']){
                    $nameShow = "You:";
                }else{
                    $nameShow = $general->getUserNameByID($rows['uid']).":";
                }
            ?>
            <div id="drtails">
            <?php
                echo '<blockquote class="blockquote"><p class="fs-6">'.$nameShow.'</p><p class="mb-3">' . nl2br($rows['description']) . '</p></blockquote>';

                if($rows['files'] != ""){
                    echo "<p class='fs-6'>Files</p>";
                    $files = explode(",", $rows['files']);
                    foreach ($files as $file) {
                        echo "<div><a class='text-primary' href='" . siteurl . "/uploads/" . $file . "'>Download File</a></div>";
                    }
                }
                // echo '<p class="blockquote-footer">' . $general->dateToRead($rows['created']) . '</p>';
                ?>
        <hr>
            </div>
            <?php
            }
            ?>
        </div>

        <?php



    }

    if ($_GET['action'] == "viewAgent") {
        $id        = mysqli_real_escape_string($connection, $_GET['id']);
        $query = "SELECT * FROM `users` WHERE `id`=" . $id;
        $sqlQuery = mysqli_query($connection, $query);
        $rows = mysqli_fetch_assoc($sqlQuery);
        $package = $general->getAgentPackageById($rows['agentPackage']);
        // SELECT `id`, `full_name`, `date_of_birth`, `nationality`, `contact_number`, `email_address`, `current_address`, `passport_number`, `passport_expiry_date`, `current_occupation`, `previous_experience`, `passport_copy`, `passport_photo`, `certificates`, `declaration`, `signature`, `submission_date`, `status`, `updated`, `created` FROM `expression_of_interest` WHERE 1
        ?>
        <div class="mb-4">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-4">
                        <label class="form-label fs-4 fw-bolder">Agent Package</label>
                        <div><?php echo $package['package_name']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-4">
                        <label class="form-label fs-4 fw-bolder">Total Visa Applications</label>
                        <div><?php echo $general->getUserTotalVisaApplications($id); ?></div>
                    </div>
                </div>
                
                <!-- line in bottom -->
                <!-- <span class="border-bottom mb-4 w-100">&nbsp;</span> -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fs-4 fw-bolder">Full Name</label>

                        <div><?php echo $rows['name']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="contactNumber" class="form-label fs-4 fw-bolder">Contact Number</label>
                        <div><?php echo $rows['phone']; ?></div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emailAddress" class="form-label fs-4 fw-bolder">Email Address</label>
                        <div><?php echo $rows['email']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="currentAddress" class="form-label fs-4 fw-bolder">Country</label>
                        <div><?php echo $general->getCountryNameByiso2($rows['country']); ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="currentAddress" class="form-label fs-4 fw-bolder">Industry</label>
                        <div><?php echo $rows['industry']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="currentAddress" class="form-label fs-4 fw-bolder">Contact Person</label>
                        <div><?php echo $rows['contactPerson']; ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Identification -->
         <?php 
    }
    if ($_GET['action'] == "viewVisaApplicationAddtionalDetails") {
        $id        = mysqli_real_escape_string($connection, $_GET['id']);
        ?>
        <div class="mb-3 bg-gray-100 p-3">
            <h3 class="mb-4">Additional Details</h3>
            <?php
            $query = "SELECT * FROM `visaApplicationAddInfo` WHERE `visaID`=" . $id;
            // echo $query;
            $sqlQuery = mysqli_query($connection, $query);
            while($rows = mysqli_fetch_assoc($sqlQuery)){
                if(userid == $rows['uid']){
                    $nameShow = "You:";
                }else{
                    $nameShow = $general->getUserNameByID($rows['uid']).":";
                }
            ?>
            <div id="drtails">
            <?php
                echo '<blockquote class="blockquote"><p class="fs-6">'.$nameShow.'</p><p class="mb-3">' . nl2br($rows['description']) . '</p></blockquote>';

                if($rows['files'] != ""){
                    echo "<p class='fs-6'>Files</p>";
                    $files = explode(",", $rows['files']);
                    foreach ($files as $file) {
                        echo "<div><a class='text-primary' href='" . siteurl . "/uploads/" . $file . "'>Download File</a></div>";
                    }
                }
                // echo '<p class="blockquote-footer">' . $general->dateToRead($rows['created']) . '</p>';
                ?>
                <hr>
            </div>
            <?php
            }
            ?>
        </div>

    <?php



    }

    if ($_GET['action'] == "viewAgentData") {
        $id        = mysqli_real_escape_string($connection, $_GET['id']);
        $query = "SELECT * FROM `expression_of_interest` WHERE `id`=" . $id;
        $sqlQuery = mysqli_query($connection, $query);
        $rows = mysqli_fetch_assoc($sqlQuery);
        // SELECT `id`, `full_name`, `date_of_birth`, `nationality`, `contact_number`, `email_address`, `current_address`, `passport_number`, `passport_expiry_date`, `current_occupation`, `previous_experience`, `passport_copy`, `passport_photo`, `certificates`, `declaration`, `signature`, `submission_date`, `status`, `updated`, `created` FROM `expression_of_interest` WHERE 1
    ?>
        <div class="mb-4">
            <h5 class="mb-3 fs-3">Personal Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Full Name</label>

                        <div><?php echo $rows['full_name']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="dob" class="form-label">Date of Birth</label>
                        <div><?php echo $general->dateToRead($rows['date_of_birth']); ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="nationality" class="form-label">Nationality</label>
                        <div><?php echo $rows['nationality']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="contactNumber" class="form-label">Contact Number</label>
                        <div><?php echo $rows['contact_number']; ?></div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="emailAddress" class="form-label">Email Address</label>
                        <div><?php echo $rows['email_address']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="currentAddress" class="form-label">Current Address</label>
                        <div><?php echo $rows['current_address']; ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Identification -->
        <div class="mb-4">
            <h5 class="mb-3 fs-3">Identification</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="passportNumber" class="form-label">Passport Number</label>
                        <div><?php echo $rows['passport_number']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="passportExpiry" class="form-label">Passport Expiry Date</label>
                        <div><?php echo $general->dateToRead($rows['passport_expiry_date']); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Background -->
        <div class="mb-4">
            <h5 class="mb-3 fs-3">Professional Background</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="currentOccupation" class="form-label">Current Occupation</label>
                        <div><?php echo $rows['current_occupation']; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="relevantExperience" class="form-label">Previous Relevant Experience</label>
                        <div><?php echo $rows['previous_experience']; ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supporting Documents -->
        <div class="mb-4">
            <h5 class="mb-3 fs-3">Supporting Documents (Attach Copies)</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="passportCopy" class="form-label">Copy of your Passport</label>
                        <div><?php echo "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $rows['passport_copy'] . "'>Download</a>"; ?></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="passportPhoto" class="form-label">Recent Passport-Sized Photograph</label>
                        <div><?php echo "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $rows['passport_photo'] . "'>Download</a>"; ?></div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="certificates" class="form-label">Relevant Certificates or Qualifications</label>
                        <!--  -->
                        <?php
                        // $rows['certificates'] has comma seperated values show all items
                        $certificates = explode(",", $rows['certificates']);
                        foreach ($certificates as $certificate) {
                            echo "<div><a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $certificate . "'>Download</a></div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Declaration -->
        <div class="mb-4">
            <h5 class="mb-3 fs-3">Declaration</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="signature" class="form-label">Signature</label>

                        <div><img style="width: 150px;" src="<?php echo siteurl . "/uploads/signature/" . $rows['signature']; ?>" alt="Signature" class="img-fluid"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label">Date</label>
                        <div><?php echo $general->dateToRead($rows['submission_date']); ?></div>
                    </div>
                </div>
            </div>
        </div>

<?php

        exit;
    }
}
