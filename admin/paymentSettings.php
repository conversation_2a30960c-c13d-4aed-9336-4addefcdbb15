<?php
$page = "Payment Settings";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION['msg']) && $_SESSION['msg'] != '') {
    $message = $_SESSION['msg'];
    unset($_SESSION['msg']);
  }
if(isset($_GET['action']))
    {
        if($_GET['action'] == "add")
        {
            $form = "add";
        }
        elseif($_GET['action'] == "edit")
        {
            $form = "edit";
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select * from settings where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $results    = mysqli_fetch_array($strSQL);
                // print_r($results);
                $name      = $results['name'];
                $value      = $results['value'];
                
                

                
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid action
                </div>';
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=paymentSettings.php" />';
                    exit; 
            }
        }
        elseif($_GET['action'] == "delete")
        {
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id from settings where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $strSQL = mysqli_query($connection,"delete from settings where id='".$id."'");
                $message ='<div class="alert alert-danger" role="alert">
                    Setting deleted
                </div>'; 
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=paymentSettings.php" />';
                    exit;    
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid action
                </div>';
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=paymentSettings.php" />';
                    exit; 
            }    
        }
    }
    if(isset($_POST['action']))
    {
        if($_POST['action'] == "add")
        {
            
            $name       = mysqli_real_escape_string($connection,$_POST['name']);
            $value       = mysqli_real_escape_string($connection,$_POST['value']);
            
           
                
                mysqli_query($connection,"insert into settings(`name`,`value`) values('".$name."','".$value."')");
                $id = mysqli_insert_id($connection);
                
                $message ='<div class="alert alert-success" role="alert">
                Setting Added
                </div>';
                $id = mysqli_insert_id($connection);                
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=paymentSettings.php" />';
                    exit; 
            
        }
        if($_POST['action'] == "edit")
        {
            // $name       = mysqli_real_escape_string($connection,$_POST['name']);
            $value       = mysqli_real_escape_string($connection,$_POST['value']);
            
            $id     = mysqli_real_escape_string($connection,$_POST['id']);
            
            
            $query = "update settings set value='$value' where id='".$id."'";
            
            $result = mysqli_query($connection,$query);
            $message ='<div class="alert alert-success" role="alert">
                Setting updated
            </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=paymentSettings.php" />';
            exit;   
        
        }
        else
        {
            $message ='<div class="alert alert-danger" role="alert">
                Error occured please try again
            </div>';
            $_SESSION['msg'] = $message;
                echo '<meta http-equiv="refresh" content="0;url=paymentSettings.php" />';
                exit; 
        }
    }

?>
<link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">

<!-- page content -->
<div class="content-wrapper">
            <section class="content-header">
               <div class="header-icon">
                  <i class="fa fa-dollar"></i>
               </div>
               <div class="header-title">
                  <h1>Payment Settings</h1>
                  <small>&nbsp;</small>
               </div>
            </section>
            <section class="content">
               


<!-- 
    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                  <a href="?action=add"  class="btn bb-bg btn-primary bb-bg mt-2 mt-xl-0"><i class="fa fa-plus"></i> Add Setting</a>
                </div>
            </div>
        </div>
    </div>
    <br> -->
    <?php echo isset($message) ? $message : ''; //echo $general->myJWT();?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                <?php if(isset($form)) {?>
                    <div class="container">
                <form id="form" action="paymentSettings.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                  <div class="form-group">
                    <?php echo isset($name) ? $name : ''; ?>
                  </div>
                  
                  <div class="form-group">
                    Value: <input type="text" name="value" value="<?php echo isset($value) ? $value : ''; ?>" class="form-control form-control-lg" placeholder="" required="required">
                  </div>
                  
                  <?php if($form == "edit"){ ?>
                          <input type="hidden" name="id" value="<?php echo $id; ?>">
                          <?php } ?>
                  <div class="mt-3">
                    <button type="submit" class="btn btn-block bb-bg btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                  </div>
                </form>
              </div>
                <?php }else{ ?>
                    <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover" id="myTable1">
                    <thead class="back_table_color">
                                <tr>
                                    <th style="width: 10%;">ID</th>
                                    <th>Name</th>
                                    <th>Value</th>
                                    <th style="width: 17%;"></th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php
                        $query = "SELECT * FROM settings";
                        // echo $query;
                        $result = mysqli_query($connection,$query);
                        $idd = 1;
                        while($rows=mysqli_fetch_array($result))
                        {
                            
                            $action = "<a href='?action=edit&id=".$rows['id']."' class='label-info label label-default'><i class='fa fa-pencil'></i> Edit </a>";
                            
                            if($rows['nameSlug'] == "paypal_payments"){
                                if($rows['value'] == 'live'){
                                    $check = "checked";
                                }else{
                                    $check = "";
                                }
                                $rows['value'] = '<input type="checkbox" name="paypal_payments" '.$check.' value="'.$rows['value'].'" id="paypal_payments" data-toggle="toggle" data-on="Live" data-off="Sandbox" data-onstyle="primary" data-offstyle="warning">';
                                $action = "";
                            }
                            
                            echo "<tr>
                                    <td>".$idd."</td>
                                    <td>".$rows['name']."</td>
                                    <td>".$rows['value']."</td>
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                            $idd++;
                        }
                        ?>
                                
                            </tbody>
                        </table>
                    </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>

            </section>
</div>
<!-- /page content -->
<?php

include("includes/footer.php");
?>
<script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>
<script>
    $(document).ready(function() {
        $('#paypal_payments').change(function() {
            if ($(this).is(':checked')) {
                var value = 'live';
            } else {
                var value = 'sandbox';
            }
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'POST',
                data: {
                    action: 'updatePaypalPayments',
                    value: value
                },
                success: function(response) {
                    console.log(response);
                }
            });
        });
    });
</script>
