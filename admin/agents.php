<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");
$general = new generalFunctions($connection);

if (isset($_SESSION['msg']) && $_SESSION['msg'] != '') {
    $message = $_SESSION['msg'];
    unset($_SESSION['msg']);
} else {
    $message = '';
}

if (isset($_GET['action'])) {
    if ($_GET['action'] == "add") {
        $form = "add";
    } elseif ($_GET['action'] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select * from users where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            // print_r($results);
            $name                   = $results['name'];
            $lname                  = $results['lname'];
            $email                  = $results['email'];
            $status                 = $results['status'];
            $type                   = $results['type'];
            $phone                  = $results['phone'];
            $contactPerson          = $results['contactPerson'];
            $country                = $results['country'];
            $agentPackage           = $results['agentPackage'];
            $uid                    = $results['id'];
            
            
            $eoiData = $general->getEOIData($uid);
            $dob                    = $eoiData['date_of_birth'];
            $nationality            = $eoiData['nationality'];
            $current_address        = $eoiData['current_address'];
            $passport_number        = $eoiData['passport_number'];
            $passport_expiry_date   = $eoiData['passport_expiry_date'];
            $current_occupation     = $eoiData['current_occupation'];
            $previous_experience    = $eoiData['previous_experience'];
            $declaration            = $eoiData['declaration'];
            $signature              = $eoiData['signature'];
            $submission_date        = $eoiData['submission_date'];
            $license_num            = $eoiData['license_num'];
            $l_expiry               = $eoiData['l_expiry'];
            $companyName            = $eoiData['companyName'];

            $passport_copy          = $eoiData['passport_copy'];
            $passport_photo         = $eoiData['passport_photo'];
            $certificates           = $eoiData['certificates'];
            $ID_document            = $eoiData['ID_document'];
            $license_file           = $eoiData['license_file'];
            $Company_details           = $eoiData['Company_details'];
            

        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid agent
                </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        }
    } elseif ($_GET['action'] == "deleteLicense") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "SELECT id from agentLicense where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "DELETE from agentLicense where id='" . $id . "'");
            $message = '<div class="alert alert-danger" role="alert">
                    License deleted
                </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid agent
                </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from users where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from users where id='" . $id . "'");
            $message = '<div class="alert alert-danger" role="alert">
                    Agent deleted
                </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid agent
                </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        }
    }
}
// print_r($_POST);
if (isset($_POST['action'])) {
    if ($_POST['action'] == "addLicense") {
        $id = mysqli_real_escape_string($connection, $_POST['id']);
        $license = mysqli_real_escape_string($connection, $_POST['license']);
        $expiry = mysqli_real_escape_string($connection, $_POST['expiry']);
        // select if exist update else insert
        $sql = "SELECT * FROM `agentLicense` WHERE uid = '$id'";
        $result = mysqli_query($connection, $sql);
        if (mysqli_num_rows($result) > 0) {
            $sql = "UPDATE `agentLicense` SET `license`='$license',`expiry`='$expiry' WHERE uid = '$id'";
            mysqli_query($connection, $sql);
            $message = '<div class="alert alert-success" role="alert">
                    License Updated
                </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        }

        $sql = "INSERT INTO `agentLicense`(`uid`, `license`, `expiry`) VALUES ('$id','$license','$expiry')";
        mysqli_query($connection, $sql);
        $message = '<div class="alert alert-success" role="alert">
                    License Added
                </div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
        exit;
    }

    if ($_POST['action'] == "add") {
        // print_r($_POST);
        // exit;

        $name       = mysqli_real_escape_string($connection, $_POST['full_name']);
        $lname       = " ";
        $email       = mysqli_real_escape_string($connection, $_POST['email']);

        $phone       = mysqli_real_escape_string($connection, $_POST['contact_number']);
        $status      = mysqli_real_escape_string($connection, $_POST['status']);

        $country      = mysqli_real_escape_string($connection, $_POST['nationality']);
        $type      = 3;
        $agentPackage      = mysqli_real_escape_string($connection, $_POST['agentPackage']);
        $password      = mysqli_real_escape_string($connection, $_POST['password']);


        $full_name             = mysqli_real_escape_string($connection, $_POST['full_name']);
        $date_of_birth         = $general->dateToDB(mysqli_real_escape_string($connection, $_POST['dob']));
        $nationality           =  mysqli_real_escape_string($connection, $_POST['nationality']);
        $contact_number        = mysqli_real_escape_string($connection, $_POST['contact_number']);
        $email_address         = mysqli_real_escape_string($connection, $_POST['email']);
        $current_address       = mysqli_real_escape_string($connection, $_POST['current_address']);
        $passport_number       = mysqli_real_escape_string($connection, $_POST['passport_number']);
        $passport_expiry_date  = $general->dateToDB(mysqli_real_escape_string($connection, $_POST['passport_expiry']));
        $current_occupation    = mysqli_real_escape_string($connection, $_POST['current_occupation']);
        $previous_experience   = mysqli_real_escape_string($connection, $_POST['relevant_experience']);
        $license_num           = mysqli_real_escape_string($connection, $_POST['license_num']);
        $l_expiry              = $general->dateToDB(mysqli_real_escape_string($connection, $_POST['l_expiry']));
        $companyName           = mysqli_real_escape_string($connection, $_POST['companyName']);

        

        // $passwordRW  = substr(md5(microtime()),rand(0,26),6);
        $passwordRW  = $password;
        $password    = password_hash($passwordRW, PASSWORD_DEFAULT);

        $query = "SELECT email FROM `users` where email='" . $email . "'";
        $result = mysqli_query($connection, $query);
        $numResults = mysqli_num_rows($result);

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
        {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid email address please type a valid email
                </div>';
            $form = "add";
        } elseif ($numResults > 0) {
            $message = '<div class="alert alert-danger" role="alert">
                    Account already exist
                </div>';
            $form = "add";
        } else {

            mysqli_query($connection, "INSERT into `users`(`name`, `lname`, `email`, `password`, `status`, `phone`, `type`, `country`, `agentPackage`, `createdBy`) values('" . $name . "','" . $lname . "','" . $email . "','" . $password . "','" . $status . "','" . $phone . "','" . $type . "','$country', '$agentPackage',1)");
            $id = mysqli_insert_id($connection);

            $target_dir = "../uploads/";
        $passport_photo = "";
        if($_FILES["passport_photo"]["name"] != "")
        {
            $passport_photo = $_FILES["passport_photo"]["name"];
            $extension = pathinfo($passport_photo, PATHINFO_EXTENSION);
            $passport_photo = uniqid("passport_photo_", true) . "." . $extension;
            move_uploaded_file($_FILES["passport_photo"]["tmp_name"], $target_dir . $passport_photo);
            // $passport_photo = ", `passport_photo` = '$passport_photo'";
        }
        $passport_copy = "";
        if($_FILES["passport_copy"]["name"] != "")
        {
            $passport_copy = $_FILES["passport_copy"]["name"];
            $extension = pathinfo($passport_copy, PATHINFO_EXTENSION);
            $passport_copy = uniqid("passport_copy_", true) . "." . $extension;
            move_uploaded_file($_FILES["passport_copy"]["tmp_name"], $target_dir . $passport_copy);
            // $passport_copy = ", `passport_copy` = '$passport_copy'";
        }
        $license_file = "";
        if($_FILES["license_file"]["name"] != "")
        {
            $license_file = $_FILES["license_file"]["name"];
            $extension = pathinfo($license_file, PATHINFO_EXTENSION);
            $license_file = uniqid("license_file_", true) . "." . $extension;
            move_uploaded_file($_FILES["license_file"]["tmp_name"], $target_dir . $license_file);
            // $license_file = ", `license_file` = '$license_file'";
        }
        $ID_document = "";
        if($_FILES["ID_document"]["name"] != "")
        {
            $ID_document = $_FILES["ID_document"]["name"];
            $extension = pathinfo($ID_document, PATHINFO_EXTENSION);
            $ID_document = uniqid("ID_document_", true) . "." . $extension;
            move_uploaded_file($_FILES["ID_document"]["tmp_name"], $target_dir . $ID_document);
            // $ID_document = ", `ID_document` = '$ID_document'";
        }
        $Company_details = "";
        if($_FILES["Company_details"]["name"] != "")
        {
            $Company_details = $_FILES["Company_details"]["name"];
            $extension = pathinfo($Company_details, PATHINFO_EXTENSION);
            $Company_details = uniqid("Company_details_", true) . "." . $extension;
            move_uploaded_file($_FILES["Company_details"]["tmp_name"], $target_dir . $Company_details);
            // $Company_details = ", `Company_details` = '$Company_details'";
        }
        $certificates = "";
        // certificates are multiple
        if(count($_FILES['certificates']['name']) > 0)
        {
            foreach($_FILES['certificates']['name'] as $key => $val){
                $certificates = $_FILES['certificates']['name'][$key];
                $extension = pathinfo($certificates, PATHINFO_EXTENSION);
                $certificates = uniqid("certificates_$key", true) . "." . $extension;
                move_uploaded_file($_FILES['certificates']['tmp_name'][$key], $target_dir . $certificates);
                $certificatesFiles[] = $certificates;
            }
            $certificates = implode(",", $certificatesFiles);
            // $certificates = ", `certificates` = '$certificates'";
        }

            $query = "INSERT INTO `expression_of_interest`  (`uid`, `full_name`, `date_of_birth`, `nationality`, `contact_number`, `email_address`, `current_address`, `passport_number`, `passport_expiry_date`, `current_occupation`, `previous_experience`, `passport_copy`, `passport_photo`, `certificates`, `declaration`, `signature`, `submission_date`,`status`,`license_num`,`l_expiry`,`companyName`,`agentPackage`, `license_file`, `ID_document`, `Company_details`)
            VALUES ('$id','$full_name', '$date_of_birth', '$nationality', '$contact_number', '$email_address', '$current_address', '$passport_number', '$passport_expiry_date', '$current_occupation', '$previous_experience', '$passport_copy', '$passport_photo', '$certificates', '1', '', now(), -1, '$license_num','$l_expiry','$companyName','$agentPackage', '$license_file', '$ID_document', '$Company_details')";
            // echo $query;
            mysqli_query($connection, $query);

            $message = '<div class="alert alert-success" role="alert">
                    Agent Added
                </div>';
            $id = mysqli_insert_id($connection);
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        }
    }
    if ($_POST['action'] == "edit") {
        // print_r($_POST);
        // exit;
        $error = false;
        // print_r($_POST);
        $name       = mysqli_real_escape_string($connection, $_POST['full_name']);
        $lname       = " ";
        $email       = mysqli_real_escape_string($connection, $_POST['email']);
        $phone       = mysqli_real_escape_string($connection, $_POST['contact_number']);
        $status      = mysqli_real_escape_string($connection, $_POST['status']);
        $country      = mysqli_real_escape_string($connection, $_POST['nationality']);
        $type      = 3;
        $agentPackage      = mysqli_real_escape_string($connection, $_POST['agentPackage']);



        

        $id     = mysqli_real_escape_string($connection, $_POST['id']);

        $query = "SELECT email FROM `users` where id='" . $id . "'";
        // echo $query;
        $result = mysqli_query($connection, $query);
        $results = mysqli_fetch_array($result);
        if (mysqli_num_rows($result) > 0) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
            {
                $message = '<div class="alert alert-danger" role="alert">
                        Invalid email address please type a valid email
                    </div>';
                $form = "add";
                $error = true;
            } elseif ($results['email'] != $email) {
                $query = "SELECT email FROM `users` where email='" . $email . "'";
                $result = mysqli_query($connection, $query); {
                    $numResults = mysqli_num_rows($result);
                    if ($numResults > 0) {
                        $email = $results['email'];
                        $message = '<div class="alert alert-danger" role="alert">
                                Email address already exist
                            </div>';
                        $form = "edit";
                        $error = true;
                    }
                }
            } elseif ($error == false) {
                $query = "UPDATE `users` set 
                `country`='$country', 
                `name`='$name', 
                `lname`='$lname', 
                `email`='$email', 
                `status`='$status', 
                `type`='$type', 
                `phone`='$phone', 
                `agentPackage`='$agentPackage' where `id`='" . $id . "'";
                $result = mysqli_query($connection, $query);




                $target_dir = "../uploads/";
                $passport_photo = "";
                if($_FILES["passport_photo"]["name"] != "")
                {
                    $passport_photo = $_FILES["passport_photo"]["name"];
                    $extension = pathinfo($passport_photo, PATHINFO_EXTENSION);
                    $passport_photo = uniqid("passport_photo_", true) . "." . $extension;
                    move_uploaded_file($_FILES["passport_photo"]["tmp_name"], $target_dir . $passport_photo);
                    $passport_photo = ", `passport_photo` = '$passport_photo'";
                }
                $passport_copy = "";
                if($_FILES["passport_copy"]["name"] != "")
                {
                    $passport_copy = $_FILES["passport_copy"]["name"];
                    $extension = pathinfo($passport_copy, PATHINFO_EXTENSION);
                    $passport_copy = uniqid("passport_copy_", true) . "." . $extension;
                    move_uploaded_file($_FILES["passport_copy"]["tmp_name"], $target_dir . $passport_copy);
                    $passport_copy = ", `passport_copy` = '$passport_copy'";
                }
                $license_file = "";
                if($_FILES["license_file"]["name"] != "")
                {
                    $license_file = $_FILES["license_file"]["name"];
                    $extension = pathinfo($license_file, PATHINFO_EXTENSION);
                    $license_file = uniqid("license_file_", true) . "." . $extension;
                    move_uploaded_file($_FILES["license_file"]["tmp_name"], $target_dir . $license_file);
                    $license_file = ", `license_file` = '$license_file'";
                }
                $ID_document = "";
                if($_FILES["ID_document"]["name"] != "")
                {
                    $ID_document = $_FILES["ID_document"]["name"];
                    $extension = pathinfo($ID_document, PATHINFO_EXTENSION);
                    $ID_document = uniqid("ID_document_", true) . "." . $extension;
                    move_uploaded_file($_FILES["ID_document"]["tmp_name"], $target_dir . $ID_document);
                    $ID_document = ", `ID_document` = '$ID_document'";
                }
                $Company_details = "";
                if($_FILES["Company_details"]["name"] != "")
                {
                    $Company_details = $_FILES["Company_details"]["name"];
                    $extension = pathinfo($Company_details, PATHINFO_EXTENSION);
                    $Company_details = uniqid("Company_details_", true) . "." . $extension;
                    move_uploaded_file($_FILES["Company_details"]["tmp_name"], $target_dir . $Company_details);
                    $Company_details = ", `Company_details` = '$Company_details'";
                }
                $certificates = "";
                // certificates are multiple
                if(count($_FILES['certificates']['name']) > 0)
                {
                    foreach($_FILES['certificates']['name'] as $key => $val){
                        $certificates = $_FILES['certificates']['name'][$key];
                        $extension = pathinfo($certificates, PATHINFO_EXTENSION);
                        $certificates = uniqid("certificates_$key", true) . "." . $extension;
                        move_uploaded_file($_FILES['certificates']['tmp_name'][$key], $target_dir . $certificates);
                        $certificatesFiles[] = $certificates;
                    }
                    $certificates = implode(",", $certificatesFiles);
                    $certificates = ", `certificates` = '$certificates'";
                }


                $full_name             = mysqli_real_escape_string($connection, $_POST['full_name']);
                $date_of_birth         = $general->dateToDB(mysqli_real_escape_string($connection, $_POST['dob']));
                $nationality           =  mysqli_real_escape_string($connection, $_POST['nationality']);
                $contact_number        = mysqli_real_escape_string($connection, $_POST['contact_number']);
                $email_address         = mysqli_real_escape_string($connection, $_POST['email']);
                $current_address       = mysqli_real_escape_string($connection, $_POST['current_address']);
                $passport_number       = mysqli_real_escape_string($connection, $_POST['passport_number']);
                $passport_expiry_date  = $general->dateToDB(mysqli_real_escape_string($connection, $_POST['passport_expiry']));
                $current_occupation    = mysqli_real_escape_string($connection, $_POST['current_occupation']);
                $previous_experience   = mysqli_real_escape_string($connection, $_POST['relevant_experience']);
                $license_num           = mysqli_real_escape_string($connection, $_POST['license_num']);
                $l_expiry              = $general->dateToDB(mysqli_real_escape_string($connection, $_POST['l_expiry']));
                $companyName           = mysqli_real_escape_string($connection, $_POST['companyName']);


                $query2 = "UPDATE `expression_of_interest` SET 
                `full_name`             = '$full_name',
                `date_of_birth`         = '$date_of_birth',
                `nationality`           = '$nationality',
                `contact_number`        = '$contact_number',
                `email_address`         = '$email',
                `current_address`       = '$current_address',
                `passport_number`       = '$passport_number',
                `passport_expiry_date`  = '$passport_expiry_date',
                `current_occupation`    = '$current_occupation',
                `previous_experience`   = '$previous_experience',
                `license_num`           = '$license_num',
                `l_expiry`              = '$l_expiry',
                `companyName`           = '$companyName'
                $passport_photo
                $passport_copy
                $license_file
                $ID_document
                $certificates
                $Company_details
                WHERE `uid`='" . $id . "'";
                // print_r($_FILES);
                // echo $query;
                // exit;
                
                $result = mysqli_query($connection, $query2);


                $message = '<div class="alert alert-success" role="alert">
                        Account updated
                    </div>';
                $_SESSION['msg'] = $message;
                echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
                exit;
            }
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=agents.php" />';
            exit;
        }
    }
}

?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-users"></i>
        </div>
        <div class="header-title">
            <h1>Agents management</h1>
            <small>Add Update Delete Agents</small>
        </div>
    </section>
    <section class="content">




        <div class="row">
            <div class="col-md-12 grid-margin">
                <div class="d-flex justify-content-between flex-wrap">
                    <div class="d-flex justify-content-between align-items-end flex-wrap">
                        <a href="?action=add" class="btn bb-bg btn-primary bb-bg mt-2 mt-xl-0"><i class="fa fa-plus"></i> Add Agent</a>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <?php echo isset($message) ? $message : ''; ?>
        <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">
                        <?php if (isset($form)) { ?>
                            <div class="container">
                                <form action="agents.php" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                    <!-- Personal Information -->
                                    <div class="mb-4">
                                        <h5 class="mb-3 fs-3">Personal Information</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="license_num" class="form-label">License Number <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="license_num" name="license_num" value="<?php echo isset($license_num) ? $license_num : ''; ?>" required>
                                                    <div class="invalid-feedback">Please provide your full name.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="l_expiry" class="form-label">License Expiry Date <span class="text-danger">*</span></label>
                                                    <input type="date" class="form-control" id="l_expiry" name="l_expiry" value="<?php echo isset($l_expiry) ? $l_expiry : ''; ?>" required>
                                                    <div class="invalid-feedback">Please provide your full name.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="fullName" class="form-label">Full Name <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="fullName" name="full_name" value="<?php echo isset($name) ? $name : ''; ?>" required>
                                                    <div class="invalid-feedback">Please provide your full name.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="dob" class="form-label">Date of Birth <span class="text-danger">*</span></label>
                                                    <input type="date" class="form-control" id="dob" name="dob" autocomplete="off" value="<?php echo isset($dob) ? $dob : ''; ?>" required>
                                                    <div class="invalid-feedback">Please select your date of birth.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="nationality" class="form-label">Nationality <span class="text-danger">*</span></label>
                                                    <select id="nationality" name="nationality" class="form-control templating" required>
                                                        <option value="">Select</option>
                                                        <?php
                                                        $sql = "SELECT * FROM `countries`";
                                                        $result = $connection->query($sql);
                                                        if ($result->num_rows > 0) {
                                                            while ($row = $result->fetch_assoc()) {
                                                                // mark selected
                                                                if (isset($country) && $row['iso2'] == $country) {
                                                                    echo '<option value="' . $row['iso2'] . '" selected>' . $row['name'] . '</option>';
                                                                } else {
                                                                    echo '<option value="' . $row['iso2'] . '">' . $row['name'] . '</option>';
                                                                }
                                                            }
                                                        }
                                                        ?>
                                                    </select>
                                                    <div class="invalid-feedback">Please select your nationality.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="contactNumber" class="form-label">Contact Number <span class="text-danger">*</span></label>
                                                    <input type="tel" class="form-control" id="contactNumber1" name="contact_number" value="<?php echo isset($phone) ? $phone : ''; ?>" pattern="^\+?[0-9]{10,15}$" required>
                                                    <div class="invalid-feedback">Please provide a valid contact number (e.g., +1234567890).</div>
                                                </div>

                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="emailAddress" class="form-label">Email Address <span class="text-danger">*</span></label>
                                                    <input type="email" class="form-control" id="emailAddress" name="email" value="<?php echo isset($email) ? $email : ''; ?>" required>
                                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                                </div>
                                            </div>

                                            <?php if ($form == "add") { ?>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                                        <input type="password" name="password" id="password" class="form-control " placeholder="Password" required="required">
                                                    </div>
                                                </div>
                                            <?php } ?>


                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                        <label for="agentPackage" class="form-label">Agent Package: <span class="text-danger">*</span></label>
                                                    <select name="agentPackage" id="agentPackage" class="form-control" required="required">
                                                        <option value="">Select </option>
                                                        <?php
                                                        $query = "SELECT id, `package_name` FROM `agent_packages`";
                                                        $result = mysqli_query($connection, $query);

                                                        while ($rows = mysqli_fetch_array($result)) {
                                                            if (isset($form) && ($form == "edit" || $form == "add") && $agentPackage == $rows['id']) {
                                                        ?>
                                                                <option selected="selected" value="<?php echo $rows['id']; ?>"><?php echo $rows['package_name']; ?></option>
                                                            <?php
                                                            } else {
                                                            ?>
                                                                <option value="<?php echo $rows['id']; ?>"><?php echo $rows['package_name']; ?></option>
                                                        <?php
                                                            }
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Account Status: <span class="text-danger">*</span></label>
                                                    <select name="status" class="form-control" required="required">
                                                        <option value="">Account Status </option>
                                                        <option <?php echo (isset($status) && $status == "1") ? 'selected="selected"' : ''; ?> value="1">Active</option>
                                                        <option <?php echo (isset($status) && $status == "2") ? 'selected="selected"' : ''; ?> value="2">Inactive</option>
                                                        <option <?php echo (isset($status) && $status == "3") ? 'selected="selected"' : ''; ?> value="3">Suspended</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <?php if ($form == "edit") { ?>
                                                <input type="hidden" name="id" value="<?php echo $id; ?>">
                                            <?php } ?>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="currentAddress" class="form-label">Current Address <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="currentAddress" name="current_address" value="<?php echo isset($current_address) ? $current_address : ''; ?>" required>
                                                    <div class="invalid-feedback">Please provide your current address.</div>
                                                </div>
                                            </div>


                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="passportNumber" class="form-label">Passport Number <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="passportNumber" name="passport_number" value="<?php echo isset($passport_number) ? $passport_number : ''; ?>" pattern="^[A-Za-z0-9]{6,9}$" required>
                                                    <div class="invalid-feedback">Please provide a valid passport number (6-9 alphanumeric characters).</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="passportExpiry" class="form-label">Passport Expiry Date <span class="text-danger">*</span></label>
                                                    <input type="date" class="form-control" id="passportExpiry" name="passport_expiry" value="<?php echo isset($passport_expiry_date) ? $passport_expiry_date : ''; ?>" autocomplete="off" required>
                                                    <div class="text-danger passportExpireMsd" style="display: none;">There is insufficient time left on your passport, please get a new passport</div>
                                                    <div class="invalid-feedback">Please provide your passport expiry date.</div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="currentOccupation" class="form-label">Current Occupation <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="currentOccupation" name="current_occupation" value="<?php echo isset($current_occupation) ? $current_occupation : ''; ?>" required>
                                                    <div class="invalid-feedback">Please provide your current occupation.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="companyName" class="form-label">Company Name </label>
                                                    <input type="text" class="form-control" id="companyName" name="companyName" value="<?php echo isset($companyName) ? $companyName : ''; ?>" >
                                                    <div class="invalid-feedback">Please provide your current occupation.</div>
                                                </div>
                                            </div>


                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label for="relevantExperience" class="form-label">Previous Relevant Experience <span class="text-danger">*</span></label>
                                                    <?php echo $general->richText("relevant_experience", isset($previous_experience) ? $previous_experience : ''); ?>
                                                    <div class="invalid-feedback">Please provide details about your relevant experience.</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Supporting Documents -->
                                    <div class="mb-4">
                                        <h5 class="mb-3 fs-3">Supporting Documents (Attach Copies)</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="passportCopy" class="form-label">Copy of your Passport <span class="text-danger">*</span> <?php echo isset($passport_copy) && $passport_copy != "" ? " $passport_copy <a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $passport_copy . "'><i class=\"fa fa-download\"></i></a>" : ''; ?></label>
                                                    <input type="file" class="form-control" id="passportCopy" name="passport_copy" accept=".jpg, .jpeg, .png, .pdf" <?php echo isset($passport_copy) ?'':'required'; ?>>
                                                    <div class="invalid-feedback">Please upload a copy of your passport.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="passportPhoto" class="form-label">Recent Passport-Sized Photograph <span class="text-danger">*</span> <?php echo isset($passport_photo) && $passport_photo != "" ? " $passport_photo <a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $passport_photo . "'><i class=\"fa fa-download\"></i></a>" : ''; ?></label>
                                                    <input type="file" class="form-control" id="passportPhoto" name="passport_photo" accept=".jpg, .jpeg, .png" <?php echo isset($passport_photo) ?'':'required'; ?>>
                                                    <div class="invalid-feedback">Please upload a recent passport-sized photograph.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="ID_document" class="form-label">Upload ID document <span class="text-danger">*</span> <?php echo isset($ID_document)  && $ID_document != ""? " $ID_document <a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $ID_document . "'><i class=\"fa fa-download\"></i></a>" : ''; ?></label>
                                                    <input type="file" class="form-control" id="ID_document" name="ID_document" accept=".jpg, .jpeg, .png, .pdf" <?php echo isset($ID_document) ?'':'required'; ?>>
                                                    <div class="invalid-feedback">Please upload ID document.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="license_file" class="form-label">Upload Agent Certificate <span class="text-danger">*</span> <?php echo isset($license_file) && $license_file != "" ? " $license_file <a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $license_file . "'><i class=\"fa fa-download\"></i></a>" : ''; ?></label>
                                                    <input type="file" class="form-control" id="license_file" name="license_file" accept=".jpg, .jpeg, .png, .pdf" <?php echo isset($license_file) ?'':'required'; ?>>
                                                    <div class="invalid-feedback">Please upload a license.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="Company_details" class="form-label">Upload Company details <?php echo isset($Company_details) && $Company_details != "" ? " $Company_details <a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $Company_details . "'><i class=\"fa fa-download\"></i></a>" : ''; ?></label>
                                                    <input type="file" class="form-control" id="Company_details" name="Company_details" accept=".jpg, .jpeg, .png, .pdf">
                                                    <div class="invalid-feedback">Please upload Company details.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="certificates" class="form-label">Relevant Certificates or Qualifications
                                                        <?php
                                                        if (isset($certificates) && $certificates != "") {
                                                            $certificatesArray = explode(",", $certificates);
                                                            foreach ($certificatesArray as $key => $val) {
                                                                echo " $val <a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $val . "'><i class=\"fa fa-download\"></i></a>";
                                                            }
                                                        }
                                                        ?>
                                                    </label>
                                                    <input type="file" class="form-control" id="certificates" name="certificates[]" multiple accept=".jpg, .jpeg, .png, .pdf">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                                            <button type="submit" class="btn btn-block bb-bg btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        <?php } else { ?>
                            <div class="table-responsive">

                                <table class="table table-bordered table-striped table-hover" id="myTable">
                                    <thead class="back_table_color">
                                        <tr>
                                            <th>S. #</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Type</th>
                                            <th>Country</th>
                                            <th>Package</th>
                                            <th>Documents</th>
                                            <th>Status</th>

                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $query = "SELECT * FROM `users` where type='3' ORDER BY id DESC";
                                        // echo $query;
                                        $result = mysqli_query($connection, $query);
                                        $serial = 1;
                                        while ($rows = mysqli_fetch_array($result)) {
                                            $rowColor = "";
                                            if ($rows['createdBy'] == 1) {
                                                $rowColor = "style='background-color: #fff8b0;'";
                                            }

                                            // <a href='#!' data-id='" . $rows['id'] . "' class='btn btn-warning btn btn-sm getLicense'><i class='fa fa-id-card'></i> License</a>
                                                $action = " <a href='#!' data-agent-id='" . $rows['id'] . "' class='btn btn-info btn-sm agent-info-btn' data-toggle='tooltip' data-placement='top' title='View Agent Information'><i class='fa fa-info-circle'></i></a>
                                                            <a href='?action=edit&id=" . $rows['id'] . "' class='btn btn-success btn btn-sm'><i class='fa fa-pencil'></i></a>
                                                            <a href='?action=delete&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this User?');\" class='btn btn-danger btn btn-sm'><i class='fa fa-trash'></i></a>";
                                            

                                            $contactPerson = "";
                                            if ($rows['type'] == 1) {
                                                $accountType = "Administrator";
                                            }
                                            if ($rows['type'] == 2) {
                                                $accountType = "User";
                                            }
                                            if ($rows['type'] == 3) {
                                                $accountType = "Agency";
                                                // if ($rows['contactPerson'] != "")
                                                //     $contactPerson = " (" . $rows['contactPerson'] . ")";
                                            }
                                            $expiredAccount = "";
                                            if ($rows['status'] == 1) {
                                                $status = "Active";
                                            }
                                            if ($rows['status'] == 2) {
                                                $status = "Inactive";
                                            }

                                            $package = $general->getAgentPackageById($rows['agentPackage']);
                                            if ($package == false) {
                                                $packageValue = "N/A";
                                            } else {
                                                $packageValue = $package['package_name'];
                                            }

                                            $eoiData                = $general->getEOIData($rows['id']);
                                            
                                            $passport_copy          = $eoiData['passport_copy'];
                                            $passport_photo         = $eoiData['passport_photo'];
                                            $certificates           = $eoiData['certificates'];
                                            $ID_document            = $eoiData['ID_document'];
                                            $license_file           = $eoiData['license_file'];
                                            $Company_details           = $eoiData['Company_details'];

                                            $documents = "";

                                            if($passport_copy != ""){
                                                $documents .= "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $passport_copy . "' data-toggle='tooltip' data-placement='top' title='Passport Copy'><i class=\"fa fa-download\"></i></a>&nbsp;";
                                            }
                                            if($passport_photo != ""){
                                                $documents .= "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $passport_photo . "' data-toggle='tooltip' data-placement='top' title='Passport Photo'><i class=\"fa fa-download\"></i></a>&nbsp;";
                                            }
                                            
                                            if($ID_document != ""){
                                                $documents .= "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $ID_document . "' data-toggle='tooltip' data-placement='top' title='ID Document'><i class=\"fa fa-download\"></i></a>&nbsp;";
                                            }
                                            if($license_file != ""){
                                                $documents .= "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $license_file . "' data-toggle='tooltip' data-placement='top' title='License File'><i class=\"fa fa-download\"></i></a>&nbsp;";
                                            }
                                            if($Company_details != ""){
                                                $documents .= "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $Company_details . "' data-toggle='tooltip' data-placement='top' title='Company Details'><i class=\"fa fa-download\"></i></a>&nbsp;";
                                            }
                                            

                                            if($certificates != ""){
                                                $certificatesArray = explode(",", $certificates);
                                                foreach ($certificatesArray as $key => $val) {
                                                    $documents .= "<a target='_blank' class='text-primary' href='" . siteurl . "/uploads/" . $val . "' data-toggle='tooltip' data-placement='top' title='Certificates'><i class=\"fa fa-download\"></i></a>&nbsp;";
                                                }
                                            }
                                            if($documents == ""){
                                                $documents = "N/A";
                                            }


                                            echo "<tr $rowColor>
                                    <td>" . $serial . "</td>
                                    <td>" . $rows['name'] . $contactPerson . "</td>
                                    <td>" . $rows['email'] . "</td>
                                    <td>" . $rows['phone'] . "</td>
                                    <td>" . $accountType . "</td>
                                    <td>" . $general->getCountryNameByiso2($rows['country']) . "</td>
                                    <td>" . $packageValue . "</td>
                                    <td>" . $documents . "</td>
                                    <td>" . $status . "</td>
                                    
                                    
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                                            $serial++;
                                        }
                                        ?>

                                    </tbody>
                                </table>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- license modal -->
<div class="modal fade" id="licenseModal" tabindex="-1" role="dialog" aria-labelledby="licenseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="form" action="agents.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                <div class="modal-header">
                    <h5 class="modal-title" id="licenseModalLabel">License Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body licenseModalBody">
                    <div class="form-group licenseModalBody">
                        <label>License Number: <span class="text-danger">*</span></label>
                        <div class="d-flex justify-content-between">
                            <input type="text" id="license" name="license" class="form-control " placeholder="License Number" required="required"><span class="deleteMe"></span>
                        </div>
                    </div>
                    <div class="form-group licenseModalBody">
                        <label>License Expiry Date: <span class="text-danger">*</span></label>
                        <input type="date" name="expiry" id="expiry" class="form-control " placeholder="License Expiry Date" required="required">
                    </div>
                    <input type="hidden" name="id" id="licenseUserId" value="">
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" name="action" value="addLicense" class="btn btn-primary">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Agent Information Modal -->
<div class="modal fade" id="agentInfoModal" tabindex="-1" role="dialog" aria-labelledby="agentInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="agentInfoModalLabel">
                    <i class="fa fa-user-circle"></i> Agent Information
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="agentInfoContent">
                <div class="text-center">
                    <div class="spinner-border text-info" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Loading agent information...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php
include("includes/footer.php");
?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/css/intlTelInput.css" />
<style>
    .form-control {
        display: block;
        width: 100%;
    }

    .was-validated .form-control:invalid {
        border-color: #dc3545;
    }

    .was-validated .form-control:valid {
        border-color: #198754;
    }

    /* select2 templating */
    #country-drop ul {
        list-style: none;
    }

    .select2-results__option img {
        width: 20px;
        margin-right: 10px;
    }

    .select2-container--default .select2-results>.select2-results__options {
        max-height: 200px;
        overflow-y: auto;
        width: 100%;
        margin-left: 0px !important;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #444;
        line-height: 44px;
    }

    .select2-container--default .select2-selection--single {
        background-color: #fff;
        border: 1px solid #aaa;
        border-radius: 4px;
        height: 44px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 44px;
        position: absolute;
        top: 1px;
        right: 1px;
        width: 20px;
    }

    .select2-container {
        width: 100% !important;
    }

    .iti--allow-dropdown {
        width: 100%;
    }

    /* rich text */
    .wysiwyg {
        border-radius: 8px;
        width: 100%;
        max-height: 100%;
        box-shadow: 0 0 25px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .wysiwyg .btns {
        padding: 0.3em 0.3em;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        border-bottom: 1px solid #EFEFEF;
        background-color: white;
        flex-grow: 2;
    }

    .wysiwyg .btns .category {
        display: flex;
        align-items: center;
        border-right: 1px solid #EFEFEF;
        padding: 0 4px;
    }

    .wysiwyg .editor {
        resize: none;
        background-color: white;
        outline-style: none;
        border: none;
        width: 100%;
        box-sizing: border-box;
        padding: 10px 10px;
        max-height: calc(80vh);
        overflow: auto;
        height: 150px;
        font-size: 13px;
        border: 1px solid #c3c3c3;
    }

    .wysiwyg .editor img {
        max-width: 100%;
    }

    .btn-dark {
        color: #fff;
        background-color: #343a40;
        border-color: #343a40;
        margin: 1px;
        width: 32px;
        padding: 0;
    }

    .ac .btn-light {
        width: 70px !important;
    }

    .col-1 {
        max-width: 5%;
    }

    .col-2 {
        max-width: 10.666667%;
    }
</style>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/intlTelInput.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.16/js/utils.min.js"></script>
</body>
<script>
    // country dropdown mark selected
    var country = "<?php echo isset($country) ? $country : ''; ?>";
    if (country != "") {
        $("#country").val(country);
    }

    // license modal
    $(document).on('click', '.getLicense', function() {
        $('#license').val("");
        $('#expiry').val("");
        $('.deleteMe').html("");
        // get license data if exist
        $.ajax({
            url: 'ajaxLoad.php',
            type: 'post',
            data: {
                action: 'getLicense',
                id: $(this).data('id')
            },
            success: function(response) {
                // var data = JSON.parse(response);
                console.log(response);
                if (response != null) {
                    $('#license').val(response.license);
                    $('#expiry').val(response.expiry);
                    $('.deleteMe').html('<a href="?action=deleteLicense&id=' + response.id + '" onclick="return confirm(\'Are you sure you want to DELETE this license?\');" class="btn btn-danger btn btn-sm"><i class="fa fa-trash"></i></a>');
                }
            }
        })
        var id = $(this).data('id');
        $('#licenseUserId').val(id);
        $('#licenseModal').modal('show');
    });
</script>

<script>
    $(document).ready(function() {
        // tooltip
        $('[data-toggle="tooltip"]').tooltip();

        // Handle agent info button click
        $(document).on('click', '.agent-info-btn', function(e) {
            e.preventDefault();
            var agentId = $(this).data('agent-id');

            // Show modal
            $('#agentInfoModal').modal('show');

            // Reset modal content to loading state
            $('#agentInfoContent').html(`
                <div class="text-center">
                    <div class="spinner-border text-info" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Loading agent information...</p>
                </div>
            `);

            // Fetch agent details via AJAX
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                dataType: 'html',
                data: {
                    action: 'getAgentInfoAdmin',
                    id: agentId
                },
                success: function(response) {
                    $('#agentInfoContent').html(response);
                },
                error: function() {
                    $('#agentInfoContent').html(`
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-triangle"></i>
                            Error loading agent information. Please try again.
                        </div>
                    `);
                }
            });
        });
        $("#emailAddress").on('blur', function() {

            var email = $(this).val();
            $.ajax({
                url: "ajaxLoad.php",
                type: "POST",
                data: {
                    action: "checkEmail",
                    email: email
                },
                success: function(response) {

                    if (response.status == "error") {
                        // submitButton disable 
                        $(".submitButton").prop('disabled', true);
                        $("#emailAddress").addClass("is-invalid");
                        $("#emailAddress").next().text("Email already exist.");
                    } else {
                        $(".submitButton").prop('disabled', false);
                        $("#emailAddress").removeClass("is-invalid");
                        $("#emailAddress").next().text("Please enter a valid email address.");
                    }
                },
                error: function(xhr, status, error) {
                    console.log(xhr.responseText);
                }
            });
        });
        $(document).on('click', '.closeModal', function() {
            $('#signatureModal').modal('toggle');
        });
        // passport expiry must be 6 onths time
        $('#passportExpiry').datepicker({
            uiLibrary: 'bootstrap5',
            format: 'dd/mm/yyyy',
            iconsLibrary: 'fontawesome',
            weekStartDay: 1,
            showRightIcon: false,
        });

        // passport must be valid for 6 months if date not match show error
        $('#passportExpiry').change(function() {
            // moment().add(6, 'months').format('YYYY-MM-DD')
            // moment() 17/01/2022 compare with today - 6 months

            var expiry = moment($('#passportExpiry').val(), 'DD/MM/YYYY');
            var today = moment();
            var sixMonths = moment().add(6, 'months');
            if (expiry.isBefore(sixMonths)) {
                $('#passportExpiry').addClass('is-invalid border-danger');
                $('#passportExpiry').removeClass('is-valid border-success');
                // show error Please provide your passport expiry date.
                $('.passportExpireMsd').show();
                $(".submitButton").prop('disabled', true);

            } else {
                $('#passportExpiry').addClass('is-valid border-success');
                $('#passportExpiry').removeClass('is-invalid border-danger');
                $('.passportExpireMsd').hide();
                $(".submitButton").prop('disabled', false);

            }
        });
        // $('#dob').datepicker({
        // 	uiLibrary: 'bootstrap5',
        // 	format: 'dd/mm/yyyy',
        // 	iconsLibrary: 'fontawesome',
        // 	weekStartDay: 1,    
        //     showRightIcon: false,
        // });

        var input = document.querySelector("#contactNumber");
        window.intlTelInput(input, {
            initialCountry: "auto",
            autoHideDialCode: false,
            nationalMode: false,
        });

        $(".signature_pad").on('click', function() {
            $('#signatureModal').modal('toggle');
        })
        var canvas = document.querySelector("canvas");
        var signaturePad = new SignaturePad(canvas);

        $("#save_signature").click(function() {


            if (signaturePad.isEmpty()) {
                alert("Please provide signature first.");
            } else {
                var dataURL = signaturePad.toDataURL();
                $(".signature").attr("src", dataURL);
                $(".signature").val(dataURL);
                $('#signatureModal').modal('toggle');
            }
        })
        $("#clear_signature").click(function() {
            signaturePad.clear();
        })
    });
    // Bootstrap form validation
    (function() {
        'use strict';
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();

    // Select2 templating
    function formatState(state) {
        // alert(state);
        if (!state.id) {
            return state.text;
        }
        // var baseUrl = "/user/pages/images/flags";
        var baseUrl = "<?php echo siteurl; ?>/assets/images/flags";
        var $state = $(
            '<span><img src="' + baseUrl + '/' + state.element.value.toLowerCase() + '.svg" class="img-flag" /> ' + state.text + '</span>'
        );
        return $state;
    };
    $(document).ready(function() {
        $(".templating").select2({
            templateResult: formatState
        });
        $(document).on('select2:open', () => {
            document.querySelector('.select2-search__field').focus();
        });
    });
</script>
<!-- rich text -->
<script>
    $(document).ready(function(evt) {
        let color = $('#colorPicker').on("input", function() {

            var color = $(this).val();
            execCmd('foreColor', color);
        });
        $('.richEditor').on('input', function() {
            $(this).next('textarea').val($(this).html());
        });
        $('.richEditor').on('keydown', function(e) {
            if (e.key === 'Tab') {
                e.preventDefault();
                handleTabKey();
            }
        });
        // FIXME : enable image & table resize
        document.execCommand("enableAbsolutePositionEditor", false, true)
        document.execCommand("enableInlineTableEditing", false, true)
        document.execCommand("enableObjectResizing", false, true)

        // TODO : enable insert table

        $('.btns button').click(function(evt) {
            evt.preventDefault();
            let value = null

            execCmd($(this).data('cmd'), value);

        });
    })

    function handleTabKey() {
        var selection = window.getSelection();
        var range = selection.getRangeAt(0);
        var startNode = range.startContainer;

        // Check if the Tab key is pressed inside a list item
        if (startNode.nodeName === 'LI') {
            var listType = getClosestListType(startNode);
            var newListItem = document.createElement('li');
            newListItem.innerHTML = '&nbsp;&nbsp;&nbsp;&nbsp;'; // Adjust the content as needed

            // Check if the parent list is an ordered or unordered list
            if (listType === 'OL' || listType === 'UL') {
                // Create a new line and insert it before the new list item
                var newLine = document.createElement('div');
                newLine.innerHTML = '<br>';
                range.setStartBefore(startNode);
                range.insertNode(newLine);

                // Create a nested list and append the new list item
                var nestedList = document.createElement(listType);
                nestedList.appendChild(newListItem);
                range.deleteContents();
                range.insertNode(nestedList);
            } else {
                // If not inside an ordered or unordered list, insert a new line and the new list item
                range.insertNode(document.createElement('br'));
                range.insertNode(newListItem);
            }
        } else {
            // Insert spaces if not inside a list item
            document.execCommand('insertHTML', false, '&nbsp;&nbsp;&nbsp;&nbsp;');
        }


    }

    function getClosestListType(node) {
        while (node && node.nodeName !== 'OL' && node.nodeName !== 'UL') {
            node = node.parentNode;
        }
        return node ? node.nodeName : null;
    }


    function execCmd(cmd, value) {
        document.execCommand(cmd, false, value);
    }
</script>

</html>