<?php
include_once 'includes/config.php';
include_once 'includes/generalFunctions.class.php';
$generalFunctions = new generalFunctions($connection);
$title = "FCO NEC Program";


if (isset($_COOKIE['userid']) && $_COOKIE['userid'] != "") {
    include_once 'includes/session.php';
    include_once 'includes/header.php';
    $name = name;
} else {
    include_once 'includes/header_s.php';
}

if (isset($_SESSION['msg']) && $_SESSION['msg'] != '') {
    $message = $_SESSION['msg'];
    unset($_SESSION['msg']);
} else {
    $message = '';
}
?>

<style>
    .program-header {
        background: linear-gradient(135deg, #061b36 0%, #0a2447 100%);
        color: white;
        padding: 60px 0;
        margin-top: -100px;
        padding-top: 160px;
    }
    
    .program-content {
        padding: 50px 0;
    }
    
    .form-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        
    }
    
    .program-info {
        background: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .info-card {
        background: #e3f2fd;
        border-left: 4px solid #061b36;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
    }
    
    .info-card h5 {
        color: #061b36;
        margin-bottom: 10px;
    }
    
    .google-form-container {
        position: relative;
        width: 100%;
        height: 800px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .google-form-container iframe {
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 10px;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #061b36;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .breadcrumb-custom {
        background: transparent;
        padding: 0;
        margin-bottom: 20px;
    }
    
    .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .google-form-container {
            height: 600px;
        }
        
        .form-container,
        .program-info {
            padding: 20px;
        }
    }
</style>


<!-- Main Content -->
<main>
    <section class="py-2 bg-light">
        <div class="container">
            <div class="row d-flex align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-2 fs-2">FCO NEC Program</h1>
                    <p class="mb-0">National Employment Centre Program Application</p>

                </div>

            </div>
        </div>
    </section>
    <div class="program-content">
        <div class="container">
            <?php if ($message != '') { echo $message; } ?>
            
            <div class="row">
                <div class="col-lg-4">
                    <div class="program-info">
                        <h3 class="text-primary mb-4">
                            <i class="bi bi-info-circle"></i> Program Information
                        </h3>
                        
                        <div class="info-card">
                            <h5><i class="bi bi-person-badge"></i> Welcome, <?php 
                            if(isset($name))
                                echo $name."!"; 
                            ?></h5>
                            <p class="mb-0">You are applying for the FCO National Employment Centre Program. Please fill out the form completely and accurately.</p>
                        </div>
                        
                        <div class="info-card">
                            <h5><i class="bi bi-clock"></i> Application Process</h5>
                            <ul class="mb-0">
                                <li>Complete the application form</li>
                                <li>Submit required documents</li>
                                <li>Wait for review and approval</li>
                                <li>Receive program confirmation</li>
                                <li>Submit $115 USD Fee</li>
                            </ul>
                        </div>
                        
                        <div class="info-card">
                            <h5><i class="bi bi-shield-check"></i> Important Notes</h5>
                            <ul class="mb-0">
                                <li>All fields marked with * are required</li>
                                <li>Ensure all information is accurate</li>
                                <li>Upload clear, readable documents</li>
                                <li>Save your progress regularly</li>
                            </ul>
                        </div>
                        
                        <div class="info-card">
                            <h5><i class="bi bi-telephone"></i> Need Help?</h5>
                            <p class="mb-2">If you need assistance with your application:</p>
                            <p class="mb-1"><strong>Email:</strong>  <a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <!-- <p class="mb-0"><strong>Phone:</strong> Contact support</p> -->
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-8">
                    <div class="form-container">
                        <h3 class="text-primary mb-4">
                            <i class="bi bi-file-earmark-text"></i> Application Form
                        </h3>
                        
                        <div class="google-form-container">
                            <div class="loading-overlay" id="loadingOverlay">
                                <div class="text-center">
                                    <div class="spinner"></div>
                                    <p class="mt-3">Loading application form...</p>
                                </div>
                            </div>
                            <iframe 
                                src="https://docs.google.com/forms/d/e/1FAIpQLScSJ5T5jV8lbc6eexSt5r9YCiS2cAmNMYswCBc46dhLO9vG7g/viewform?embedded=true"
                                onload="hideLoading()"
                                title="FCO NEC Program Application Form">
                                Loading form...
                            </iframe>
                        </div>
                        
                        <!-- <div class="mt-3 text-muted">
                            <small>
                                <i class="bi bi-info-circle"></i> 
                                If the form doesn't load properly, you can 
                                <a href="https://docs.google.com/forms/d/e/1FAIpQLScSJ5T5jV8lbc6eexSt5r9YCiS2cAmNMYswCBc46dhLO9vG7g/viewform?pli=1" 
                                   target="_blank" class="text-primary">open it in a new window</a>.
                            </small>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// Hide loading overlay after 5 seconds as fallback
setTimeout(function() {
    hideLoading();
}, 5000);
</script>

<?php include_once 'includes/footer.php'; ?>
