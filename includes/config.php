<?php
session_start();    
$whitelist = array(
    '127.0.0.1',
    '::1'
);


if (!in_array($_SERVER['REMOTE_ADDR'], $whitelist)) {
    $host       = 'localhost';
    $user       = 'sdqhyymv_fcoVisa';
    $pass       = '^X+dSST$H+iZ';
    $database   = 'sdqhyymv_fcoVisa';

    define("siteurl","https://fcoportal.com");
    

    

    define("host", "live"); // local = disabled some features on local, live = fully functional website
} else {
    // Database connection
    $host       = 'localhost';
    $user       = 'root';
    $pass       = '12345678';
    $database   = 'fco';

    define("siteurl","http://localhost/visaportal");    

    define("host", "local"); // local = disabled some features on local, live = fully functional website
}
    
    
    $connection = mysqli_connect($host,$user,$pass,$database);
    
    
    define("adminurl",siteurl."/admin");
    define("logo",siteurl."/assets/images/logo/logo_black.svg");
    define("logoMini",siteurl."/assets/images/logo/logo_black.svg");
    
    define("SMTPHOST","smtp.mailjet.com");
    define("SMTPUSER","********************************");
    define("SMTPPASSWORD","2bfbc4f6bf3dc053e32c0609ebe13df3");
    // define("SMTPUSER","c5b000285de1f458b0504545c5b5fcf9");
    // define("SMTPPASSWORD","43ecc00ce7ca0749d98d71dcef383d62");

    define("sitename","Foreign & Commonwealth Office"); 
    define("webMaster","<EMAIL>"); 

    $sql = "select * from settings where nameSlug = 'paypal_payments'";
    $result = mysqli_query($connection, $sql);
    if(mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        if($row['value'] == 'live') {
            define("PAYPALMODE", "live");
            define("PAYPALCLIENTID","AdloXzHVOE8D9egL-ajfrpw_TNRleFy6-ZTD_OTprTdl2E8wyyLqYDKzjBPBvWtQ4Su9JdLWfKrqb00k");
            define("PAYPALSECRET","EJZBPUXhPrPfEvfTCjjx0tOOGVGw7fXoy2f-2L0ohxUHwmWWKzapXv0vuIHWq-Ga5nxXl7o2XNha2nCb");
            define("PAYPALAPIURL","https://api-m.paypal.com");
        } else {
            define("PAYPALMODE", "sandbox");
            define("PAYPALCLIENTID","AXCt98TiV61GmM6b2vBcrCkhAJPMcvxA7oPyVM9PujckuD8LzOpsaTkCcfEy2JM-5TUn5QfvtLRLv_3w");
            define("PAYPALSECRET","EJjwUFMPnSnT4eTizFNq8RpWKrGQV_m3kOpSGZqWjfdYFU6WNRCZbFPI0F1zFnpCWRRyvCPBg5v2PFJT");
            define("PAYPALAPIURL","https://api-m.sandbox.paypal.com");
        }
    } else {
        define("PAYPALMODE", "sandbox");
        define("PAYPALCLIENTID","AXCt98TiV61GmM6b2vBcrCkhAJPMcvxA7oPyVM9PujckuD8LzOpsaTkCcfEy2JM-5TUn5QfvtLRLv_3w");
        define("PAYPALSECRET","EJjwUFMPnSnT4eTizFNq8RpWKrGQV_m3kOpSGZqWjfdYFU6WNRCZbFPI0F1zFnpCWRRyvCPBg5v2PFJT");
        define("PAYPALAPIURL","https://api-m.sandbox.paypal.com");
    }
    
    
    


function alert($type,$message)
{
    return "<div class='alert alert-$type'>$message</div>";
}    
function notify($message)
{
    return '
    <div class="content-wrapper">
        <div class="row">
            <div class="col-md-12 grid-margin">
                        <div class="alert alert-danger" role="alert">
                            '.$message.'
                        </div>
                
            </div>
        </div>
    </div>';
}

// Function to sanitize input and prevent XSS
function clean($data) {
    // Strip HTML tags
    $data = strip_tags($data);
    // Convert special characters to HTML entities
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

?>