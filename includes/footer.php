<footer class="pb-2 pt-3">
    <div class="container">
        <div class="row">
            <div class="col-lg-2">
            </div>
            <div class="col-lg-8 col-12 mb-2">
                <!-- Australia, Canada, Fiji, the United Kingdom, the United States, and New Zealand -->
                <div class="row">
                    <div class="col-lg-2 col-4 mb-2">
                        <?php echo $generalFunctions->getCountryFlagByName("Australia", 100); ?>
                    </div>
                    <div class="col-lg-2 col-4 mb-2">
                        <?php echo $generalFunctions->getCountryFlagByName("Canada", 100); ?>
                    </div>
                    <div class="col-lg-2 col-4 mb-2">
                        <?php echo $generalFunctions->getCountryFlagByName("Fiji", 100); ?>
                    </div>
                    <div class="col-lg-2 col-4 mb-2">
                        <?php echo $generalFunctions->getCountryFlagByName("United Kingdom", 100); ?>
                    </div>
                    <div class="col-lg-2 col-4 mb-2">
                        <?php echo $generalFunctions->getCountryFlagByName("United States", 100); ?>
                    </div>
                    <div class="col-lg-2 col-4 mb-2">
                        <?php echo $generalFunctions->getCountryFlagByName("New Zealand", 100); ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-2"></div>
            <div class="col-lg-2"></div>
            <div class="col-lg-3">
                <p class="small mb-0">Copyright <?php echo date("Y"); ?>. All Rights Reserved.</p>
            </div>
            <div class="col-lg-6">
                <div class="row">
                    
                    <div class="col-lg-3 col-12 mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                            class="bi bi-chevron-right" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z" />
                        </svg>
                        <?php if (isset($_COOKIE['userid']) && $_COOKIE['userid'] != "") { ?>
                            <a href="fco-portfolio.php" class="text-link">FCO portfolio</a>
                            <?php } else { ?>
                                <a href="about-us.php" class="text-link">About Us</a>
                        <?php } ?>
                    </div>
                    


                    <div class="col-lg-3 col-12 mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                            class="bi bi-chevron-right" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z" />
                        </svg>
                        <a href="contact-us.php" class="text-link">Contact Us</a>
                    </div>
                    <!-- <div class="col-lg-6 col-12 mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                            class="bi bi-chevron-right" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z" />
                        </svg>
                        <a href="affiliate.php" class="text-link">Join the referral program</a>
                    </div> -->
                    <div class="col-lg-3">
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<!-- footer close -->
<!-- footer close -->
<!-- Libs JS -->
<script src="./assets/libs/jquery/dist/jquery.min.js"></script>
<script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>


<!-- Theme JS -->
<script src="./assets/js/theme.min.js"></script>

<script src="//cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://unpkg.com/gijgo@1.9.14/js/gijgo.min.js" type="text/javascript"></script>
<link href="https://unpkg.com/gijgo@1.9.14/css/gijgo.min.css" rel="stylesheet" type="text/css" />
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://unpkg.com/slim-select@latest/dist/slimselect.min.js"></script>
<link href="https://unpkg.com/slim-select@latest/dist/slimselect.css" rel="stylesheet"></link>
<style>
    table.dataTable.no-footer
    {
        border-bottom: 1px solid #d0d0d0;
        border-top: 1px solid #d0d0d0;
    }
    .dataTables_filter
    {
        margin-bottom: 15px;
    }
    .ss-main{
        min-height: 45px !important;
    }
</style>
<script>
      const initializedSelects = new WeakSet();

      function initSlimSelects() {
        document.querySelectorAll('select.ssSelect').forEach(function(selectElement) {
          if (!initializedSelects.has(selectElement)) {
            new SlimSelect({
              select: selectElement
            });
            initializedSelects.add(selectElement);
          }
        });
      }

      const selectElements = document.querySelectorAll('select');

      if (selectElements.length > 0) {
        initSlimSelects();
      }
    </script>

<script>
    $(document).ready(function() {
        if ($("#myTable").length > 0) {
            $("#myTable").DataTable({
                "aaSorting": [],
                columnDefs: [{
                        orderable: false,
                        targets: [0, -1]
                    } // Skip sorting for the first and last columns
                ],
                pageLength: 50,
                lengthMenu: [
                    [100, 200, 500, 1000, -1],
                    [100, 200, 500, 1000, 'All']
                ],
                dom: 'frtip',
                // buttons: [
                //    'excel', 'pdf', 'print'
                // ]
            });
            $("#myTable td").css("white-space", "initial");
        }
    });
    $(function() {
        $('.selectpicker').select2({
            width: '100%'
        });
    });
    $(function() {
        $('.templating').select2({
            width: '100%'
        });
    });
    $(function() {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>
<script>
    // Menu toggle functionality
    document.addEventListener("DOMContentLoaded", () => {
        const menuOpenBtn = document.querySelector(".menu-open-btn");
        const sideNav = document.querySelector(".side-navigation");
        const subMenus = document.querySelectorAll(".has-sub-menu");

        menuOpenBtn.addEventListener("click", () => {
            const expanded = menuOpenBtn.getAttribute("aria-expanded") === "true";
            menuOpenBtn.setAttribute("aria-expanded", !expanded);
            sideNav.classList.toggle("active");
            menuOpenBtn.classList.toggle("open");

            if (!sideNav.classList.contains("active")) {
                subMenus.forEach(subMenu => subMenu.classList.remove("active"));
            }
        });

        document.querySelectorAll(".has-sub-menu > a").forEach(menuItem => {
            menuItem.addEventListener("click", (e) => {
                e.preventDefault();
                const parent = menuItem.parentElement;
                parent.classList.toggle("active");
            });
        });
    });
</script>