<?php

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require_once(__DIR__ . '/../vendor/autoload.php');
class generalFunctions
{
    public $connection;
    public $phpMailer;
    function __construct($connection)
    {
        $this->connection = $connection;
        $this->phpMailer = new PHPMailer();
    }
    function dateToDB($date)
    {
        if ($date != "") {
            $date = str_replace("/", "-", $date);
            $date = date("Y-m-d", strtotime($date));
        }
        return $date;
    }
    function fullDateToRead($date)
    {
        if ($date != "")
            $date = date("d/m/Y H:i A", strtotime($date));
        return $date;
    }
    function fulldatetoDB($input)
    {
        $date = DateTime::createFromFormat('d/m/Y H:i A', $input);
        return $date->format('Y-m-d H:i:s');
    }
    function dateToRead($date)
    {
        if ($date != "")
            $date = date("d/m/Y", strtotime($date));
        return $date;
    }
    function dateToHuman($date)
    {
        if ($date != "")
            $date = date("d-M Y", strtotime($date));
        return $date;
    }
    function getUserByID($id)
    {
        $query = "SELECT * FROM `users` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
        return "Unknown";
    }
    function getUserNameByID($id)
    {
        $query = "SELECT `name` FROM `users` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row['name'];
        }
        return "Unknown";
    }
    // number of users in the database with type=1
    function getTotalUsers()
    {
        $query = "SELECT count(id) as totalUsers FROM `users` where `type`='2'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        return $row['totalUsers'];
    }
    // total jobs
    function getTotalJobs()
    {
        $query = "SELECT count(id) as totalJobs FROM `jobs`";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        return $row['totalJobs'];
    }
    // total applications
    function getTotalApplications()
    {
        $query = "SELECT count(id) as totalApplications FROM `visaApplication` where status <> '0' and uid='" . userid . "'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        return $row['totalApplications'];
    }
    // get recent jobs
    function getRecentJobs()
    {
        $query = "SELECT * FROM `jobs` order by `id` desc limit 5";
        $result = mysqli_query($this->connection, $query);
        $jobs = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $jobs[] = $row;
        }
        return $jobs;
    }
    // get recent applications
    function getRecentApplications()
    {
        $query = "SELECT * FROM `visaApplication` WHERE `status` <> '0' ORDER BY created DESC limit 5";
        $result = mysqli_query($this->connection, $query);
        $applications = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $applications[] = $row;
        }
        return $applications;
    }

    function getNumberOfApplicationByJobID($id)
    {
        $query = "SELECT * FROM `applied` where `jobID`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        return $numResults;
    }
    function getJobByID($id)
    {
        $query = "SELECT * FROM `jobs` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
        return "Unknown";
    }
    // remove all my 0 status applied jobs
    function removeZeroStatusApplications($jobID)
    {
        $query = "DELETE FROM `applied` where `status`='0' and `userid`='" . userid . "' and jobID=$jobID";
        mysqli_query($this->connection, $query);
    }
    function getJobsByCountry($country)
    {
        $query = "SELECT * FROM `jobs` where `country`='$country' and `status`='1' order by `id` desc";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        return $numResults;
    }
    function getCountryFlagByName($name, $size = 20, $link = false)
    {
        if (!isset($size)) {
            $size = "20";
        }

        $name = ucwords($name);
        $name2 = strtolower(str_replace(" ", "_", $name));

        $query = "SELECT `iso2` FROM `countries` where `name`='$name'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            if ($link) {
                return "<a href='" . siteurl . "/" . $name2 . ".php'><img src='" . siteurl . "/assets/images/flags/" . strtolower($row['iso2']) . ".svg' alt='$name' title='$name' width='$size' class='me-3' data-toggle='tooltip' data-placement='top'></a>";
            } else {
                return "<img src='" . siteurl . "/assets/images/flags/" . strtolower($row['iso2']) . ".svg' alt='$name' title='$name' width='$size' class='me-3' data-toggle='tooltip' data-placement='top'>";
            }
        }
    }
    function getCountryNameByiso2($iso2)
    {
        $query = "SELECT `name` FROM `countries` where `iso2`='$iso2'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row['name'];
        }
    }
    function getUniversityNameByID($id)
    {
        $query = "SELECT `name` FROM `university` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row['name'];
        }
    }
    function getUniversityByID($id)
    {
        $query = "SELECT * FROM `university` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
    }
    function getCoursesByUniversityID($id)
    {
        $query = "SELECT * FROM `course` where `university_id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        return $numResults;
    }
    public function getAllCountries()
    {
        $query = "SELECT iso2, name FROM countries ORDER BY name";
        $result = mysqli_query($this->connection, $query);
        $countries = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $countries[] = $row;
        }
        return $countries;
    }
    // get country name from university
    function getCountryNameByUniversityID($id)
    {
        $query = "SELECT `countriesIn`.`name` as countryName, `university`.`name` as uni FROM `university`,`countriesIn` where countriesIn.iso2= `university`.`country` and `university`.`id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
    }

    function getCountryByUniversityID($id)
    {
        $query = "SELECT `country` FROM `university` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row['country'];
        }
    }
    function getUniversityByCountryID($id)
    {
        $query = "SELECT * FROM `university` where `country`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        return $numResults;
    }
    function getCourseByID($id)
    {
        $query = "SELECT * FROM `course` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
    }
    function getCourseNameByID($id)
    {
        $query = "SELECT `name` FROM `course` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row['name'];
        }
    }

    function getTotalUniversityCourses($id)
    {
        $query = "SELECT count(id) as totalUni FROM `courses` where `university_id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        return $numResults;
    }
    function sendEmail($name, $email, $subject, $bodyMail)
    {

        if(host == "local")
        {
            // echo "Email Sent";
            return;
        }

        $body = '
        <body marginheight="0" topmargin="0" marginwidth="0" style="margin: 0px; background-color: #f2f3f8;" leftmargin="0">
            <!-- 100% body table -->
            <table cellspacing="0" border="0" cellpadding="0" width="100%" bgcolor="#f2f3f8" style="@import url(https://fonts.googleapis.com/css?family=Rubik:300,400,500,700|Open+Sans:300,400,600,700); font-family: \'Open Sans\', sans-serif;">
                <tr>
                <td>
                    <table style="background-color: #f2f3f8; max-width:670px; margin:0 auto;" width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                    <tr>
                        <td style="height:80px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="text-align:center;">
                        <a href="' . siteurl . '" title="logo" target="_blank">
                            <img width="260" src="' . siteurl . '/assets/fcoEmailLogo.png" title="FCO" alt="logo">
                        </a>
                        </td>
                    </tr>
                    <tr>
                        <td style="height:20px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td>
                        <table width="95%" border="0" align="center" cellpadding="0" cellspacing="0" style="max-width:670px; background:#fff; border-radius:3px; -webkit-box-shadow:0 6px 18px 0 rgba(0,0,0,.06);-moz-box-shadow:0 6px 18px 0 rgba(0,0,0,.06);box-shadow:0 6px 18px 0 rgba(0,0,0,.06);">
                            <tr>
                            <td style="height:40px;">&nbsp;</td>
                            </tr>
                            <tr>
                            <td style="padding:0 35px;">
                                <p style="font-size:17px; color:#455056; margin:8px 0 0; line-height:24px;">
                                ' . $bodyMail . '
                                </p>
                            <br><br>    
                            Kind regards,<br>
                            ' . sitename . '<br>
                            <EMAIL>
                            </td>
                            </tr>
                            <tr>
                            <td style="height:40px;">&nbsp;</td>
                            </tr>
                        </table>
                        </td>
                    </tr>
                    <tr>
                        <td style="height:20px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="text-align:center;">
                        <p style="font-size:14px; color:rgba(69, 80, 86, 0.7411764705882353); line-height:18px; margin:0 0 0;"><strong><a href="' . siteurl . '" title="logo" target="_blank">' . sitename . '</a></strong></p>
                        </td>
                    </tr>
                    <tr>
                        <td style="height:80px;">&nbsp;</td>
                    </tr>
                    </table>
                </td>
                </tr>
            </table>
            <!--/100% body table-->
        </body>';

        $mail = new PHPMailer(true);

        try {
            // Server settings
            $mail->isSMTP();
            $mail->Host = SMTPHOST;
            $mail->SMTPAuth = true;
            $mail->Username = SMTPUSER;
            $mail->Password = SMTPPASSWORD;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = 587;

            // Recipients
            $mail->setFrom(webMaster, "<EMAIL>");
            $mail->addAddress($email, $name);

            // bcc <EMAIL>
            $mail->addCC('<EMAIL>', 'Support');

            // Content
            $mail->isHTML(true); // Set email format to HTML
            $mail->Subject = $subject;
            $mail->Body    = $body;

            $mail->send();
            // echo 'Message has been sent';
            return true;
        } catch (Exception $e) {
            return true;
            echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
        }
    }
    function sendEmail2($name, $email, $subject, $bodyMail,$repName,$repEmail)
    {

        // if(host == "local")
        // {
        //     echo "Email Sent";
        //     return;
        // }

        $body = '
        <body marginheight="0" topmargin="0" marginwidth="0" style="margin: 0px; background-color: #f2f3f8;" leftmargin="0">
            <!-- 100% body table -->
            <table cellspacing="0" border="0" cellpadding="0" width="100%" bgcolor="#f2f3f8" style="@import url(https://fonts.googleapis.com/css?family=Rubik:300,400,500,700|Open+Sans:300,400,600,700); font-family: \'Open Sans\', sans-serif;">
                <tr>
                <td>
                    <table style="background-color: #f2f3f8; max-width:670px; margin:0 auto;" width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                    <tr>
                        <td style="height:80px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="text-align:center;">
                        <a href="' . siteurl . '" title="logo" target="_blank">
                            <img width="260" src="' . siteurl . '/assets/fcoEmailLogo.png" title="FCO" alt="logo">
                        </a>
                        </td>
                    </tr>
                    <tr>
                        <td style="height:20px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td>
                        <table width="95%" border="0" align="center" cellpadding="0" cellspacing="0" style="max-width:670px; background:#fff; border-radius:3px; -webkit-box-shadow:0 6px 18px 0 rgba(0,0,0,.06);-moz-box-shadow:0 6px 18px 0 rgba(0,0,0,.06);box-shadow:0 6px 18px 0 rgba(0,0,0,.06);">
                            <tr>
                            <td style="height:40px;">&nbsp;</td>
                            </tr>
                            <tr>
                            <td style="padding:0 35px;">
                                <p style="font-size:17px; color:#455056; margin:8px 0 0; line-height:24px;">
                                ' . $bodyMail . '
                                </p>
                            <br><br>    
                            Kind regards,<br>
                            ' . sitename . '<br>
                            <EMAIL>
                            </td>
                            </tr>
                            <tr>
                            <td style="height:40px;">&nbsp;</td>
                            </tr>
                        </table>
                        </td>
                    </tr>
                    <tr>
                        <td style="height:20px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="text-align:center;">
                        <p style="font-size:14px; color:rgba(69, 80, 86, 0.7411764705882353); line-height:18px; margin:0 0 0;"><strong><a href="' . siteurl . '" title="logo" target="_blank">' . sitename . '</a></strong></p>
                        </td>
                    </tr>
                    <tr>
                        <td style="height:80px;">&nbsp;</td>
                    </tr>
                    </table>
                </td>
                </tr>
            </table>
            <!--/100% body table-->
        </body>';

        $mail = new PHPMailer(true);

        try {
            // Server settings
            $mail->isSMTP();
            $mail->Host = SMTPHOST;
            $mail->SMTPAuth = true;
            $mail->Username = SMTPUSER;
            $mail->Password = SMTPPASSWORD;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = 587;

            // Recipients
            $mail->setFrom(webMaster, "<EMAIL>");
            $mail->addAddress($email, $name);

            // bcc <EMAIL>
            $mail->addCC('<EMAIL>', 'Support');

            $mail->addReplyTo($repEmail, $repName);

            // Content
            $mail->isHTML(true); // Set email format to HTML
            $mail->Subject = $subject;
            $mail->Body    = $body;

            $mail->send();
            return true;
            echo 'Message has been sent';
            // exit;
        } catch (Exception $e) {
            return true;
            echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
        }
    }
    function richText($name, $value)
    {
        return '
        <div class="wysiwyg">
    <div class="btns">
      <div class="category">
        <button class="btn btn-dark" data-cmd="bold">
          <i class="fa fa-bold"></i>
        </button>
        <button class="btn btn-dark" data-cmd="italic">
          <i class="fa fa-italic"></i>
        </button>
        <button class="btn btn-dark" data-cmd="underline">
          <i class="fa fa-underline"></i>
        </button>
      </div>
      <div class="category">
        <button class="btn btn-dark" data-cmd="insertUnorderedList">
          <i class="fa fa-list-ul"></i>
        </button>
        <button class="btn btn-dark" data-cmd="insertOrderedList">
          <i class="fa fa-list-ol"></i>
        </button>

        
        
      </div>
    </div>
    <div  contentEditable class="richEditor editor">' . $value . '</div>
    <textarea class="hiddenTextarea" name="' . $name . '" style="display:none;">' . $value . '</textarea>
  </div>
        ';
    }
    function getSettingsValue($settingName)
    {
        $query = "SELECT `value` FROM `settings` where `nameSlug`='$settingName'";
        // echo $query;

        $result = mysqli_query($this->connection, $query);
        $record = mysqli_fetch_array($result);

        return $record['value'];
    }
    function visaApplicationDD($selectedId, $name)
    {
        $visaStatuses = [
            1 => "Submitted",
            2 => "Under Review",
            4 => "Background Check in Progress",
            3 => "Additional Information Required",
            5 => "Awaiting Payment",
            6 => "Approved",
            10 => "Rejected",
            7 => "Processing Complete",
            8 => "On Hold",
            9 => "Withdrawn",
            11 => "Addtional Fee Required",
            12 => "In-Process",
            13 => "Assessment Required",
            14 => "Approved by Agent",
            15 => "Rejected by Agent",
            16 => "Full Payment Received",
        ];

        echo '<select name="' . $name . '" id="' . $name . '" class="form-control form-control-lg ">';
        foreach ($visaStatuses as $id => $label) {
            $isSelected = ($id == $selectedId) ? 'selected' : '';
            echo "<option value=\"$id\" $isSelected>$label</option>";
        }
        echo '</select>';
    }
    function agentPackagesDD($selectedValue = false)
    {
        $query = "SELECT * FROM `agent_packages` ";
        $result = mysqli_query($this->connection, $query);
        $dd  = '<select class="form-control" id="packages" name="packages" required>
        <option value="">Select</option>';
        while ($rows = mysqli_fetch_array($result)) {
            $selected = ($rows['id'] === $selectedValue) ? 'selected' : '';
            $dd .= "<option value='" . $rows['id'] . "' $selected>" . $rows['package_name'] . "</option>";
        }
        $dd .= '</select>
        <div class="invalid-feedback">Please Select a Value</div>';
        return $dd;
    }
    function agentsDD($selectedValue = false)
    {
        $query = "SELECT * FROM `users` where `type`=3 and `status`=1";
        $result = mysqli_query($this->connection, $query);
        $dd  = '<select class="form-control" id="agent" name="agent" required>
        <option value="">Select</option>';
        while ($rows = mysqli_fetch_array($result)) {
            $selected = ($rows['id'] === $selectedValue) ? 'selected' : '';
            $dd .= "<option value='" . $rows['id'] . "' $selected>" . $rows['name'] . "</option>";
        }
        $dd .= '</select>
        <div class="invalid-feedback">Please Select a Value</div>';
        return $dd;
    }

    function getVisaStatus($id)
    {
        $visaStatuses = [
            1 => "Submitted",
            2 => "Under Review",
            4 => "Background Check in Progress",
            3 => "Additional Information Required",
            5 => "Awaiting Payment",
            6 => "Approved",
            10 => "Rejected",
            7 => "Processing Complete",
            8 => "On Hold",
            9 => "Withdrawn",
            11 => "Addtional Fee Required",
            12 => "In-Process",
            13 => "Assessment Required",
            14 => "Assessment Approved",
            15 => "Assessment Rejected",
            16 => "Full Payment Received",
        ];
        return $visaStatuses[$id];
    }
    function getUserResumeDownloadByID($id)
    {
        $query = "SELECT * FROM `resumes` WHERE `id`='" . $id . "' and uid='" . userid . "'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        $file = $row['file'];
        return siteurl . "/uploads/resumes/" . $file;
    }
    function isAssesmentAgentUpdated($ivsaID)
    {
        $query = "SELECT `status` from `assignedAgent` WHERE `visaID` = '$ivsaID' and `status`=1";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            return true;
        }
        return false;
    }
    function getResumeDownloadByID($id)
    {
        $query = "SELECT * FROM `resumes` WHERE `id`='" . $id . "'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        $file = $row['file'];
        return siteurl . "/uploads/resumes/" . $file;
    }
    function getFeeCountryByCountry($country, $type, $offer = false, $ass=false)
    {
        // SELECT `country`, `visaType`, `applicationFee`, `webFee`, `fcoLevy`, `commission` FROM `feesSetup` WHERE 1
        $fee = 0;
        if ($type == 2) {
            if ($offer) {
                $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `offer`='1'";
            } else {
                $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `offer`='0'";
            }
        } elseif ($type == 1) {
            $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `jobType`='$offer'";
        } elseif ($type == 4) {
            $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `otherType`='$offer'";
        } else {
            $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type'";
        }
        // echo $query;
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            $fee = $row['applicationFee'] + $row['webFee'] + $row['fcoLevy'] + $row['commission'];
            if($ass==1)
            {
                $fee = $row['assessmentFee'];
            }
            elseif($ass==2)
            {
                $fee = $row['applicationFee'] + $row['webFee'] + $row['fcoLevy'] + $row['commission']-$row['assessmentFee'];
            }

        }
        return $fee;
    }
    function getFeeCountryByCountry_Other($country, $otherType,$adult=0,$children=0)
    {
        $fee = 0;
        $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='4' and `otherType`='$otherType'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            $fee = $row['applicationFee'] + $row['webFee'] + $row['fcoLevy'] + $row['commission'] + ($row['secondaryAdultFee']*$adult) + ($row['supportedChildrenFee']*$children);
        }
        return $fee;
    }

    function getAgentPackageById($packageId)
    {

        $query = "SELECT * FROM agent_packages WHERE id = '$packageId'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        $num = mysqli_num_rows($result);
        if ($num > 0) {
            return $row;
        }
        return false;
    }

    function getMilestonesByPackageId($packageId)
    {
        $query = "SELECT * FROM milestones WHERE package_id = '$packageId' ORDER BY milestone_clients ASC";
        $result = mysqli_query($this->connection, $query);
        $milestones = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $milestones[] = $row;
        }
        return $milestones;
    }

    function getDiscountByPackageId($packageId)
    {
        $query = "SELECT * FROM package_discounts WHERE package_id = '$packageId'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        return $row;
    }

    function getAllAgentPackages()
    {
        $query = "SELECT * FROM agent_packages";
        $result = mysqli_query($this->connection, $query);
        $packages = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $packages[] = $row;
        }
        return $packages;
    }

    function getAgentPackageFee($packageId)
    {

        $query = "SELECT price FROM agent_packages WHERE id = '$packageId'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        return $row['price'];
    }
    function isVisaupdatesRequired($visaID)
    {

        $query = "SELECT * FROM `visaApplicationAddInfo` WHERE `status`=0 and `visaID`=" . $visaID;
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            return true;
        }
        return false;
    }
    function totalAgentVisaApplications()
    {
        $query = "SELECT count(id) as totalVisa FROM `visaApplication` where `uid`='" . userid . "'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        return $row['totalVisa'];
    }
    function agetEligibleFordisscount()
    {
        $query = "SELECT p.*,u.created FROM `users` `u`, `agent_packages` `a` ,`package_discounts` `p` where `u`.`agentPackage`=`p`.`id` and `a`.`id`=`p`.`package_id` and `u`.`id`=" . userid;
        // echo $query;
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        if(mysqli_num_rows($result) == 0)
        {
            return false;
        }   
        // discount should be available for 30 days after the package is purchased
        $created = $row['created'];
        $discountedPrice = $row['discount_rate'];
        $application_limit = $row['application_limit'];
        $validity_days = $row['validity_days'];
        $today = date("Y-m-d");
        $date1 = new DateTime($created);
        $date2 = new DateTime($today);
        $diff = $date1->diff($date2);
        $days = $diff->days;
        if ($days <= $validity_days && $this->totalAgentVisaApplications() <= $application_limit) {
            return $discountedPrice;
        } else {
            return false;
        }
    }
    function isAgentUser($uid)
    {
        $query = "SELECT `name` FROM `users` where `id`='$uid' and `type`='3'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        $row = mysqli_fetch_assoc($result);
        if ($numResults > 0) {
            return $row['name'];
        }
        return false;
    }
    function getAssesssmentAgentAssigned($visaID)
    {
        $query = "SELECT * FROM `assignedAgent` where `visaID`='$visaID'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        $row = mysqli_fetch_assoc($result);
        if ($numResults > 0) {
            return $row['agentID'];
        }
        return false;
    }
    function getUserTotalVisaApplications($uid)
    {
        $query = "SELECT count(id) as totalVisa FROM `visaApplication` where `uid`='" . $uid . "'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        return $row['totalVisa'];
    }
    function isJobSkilled($jobID)
    {
        $query = "SELECT `category2` FROM `jobs` where `id`='$jobID'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        if ($row['category2'] == "Skilled")
            return true;
        else
            return false;
    }

    function getFeeByVisaApplicationID($visaID)
    {
        $query = "SELECT * FROM `visaApplication` where `id`='$visaID'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        $country = $row['applyingFor'];
        $type = $row['type'];
        $offer = $row['offer'];
        $jobID = $row['jobID'];
        $otherType = $row['otherType'];
        if ($type == 2) {
            if ($offer != "") {
                $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `offer`='1'";
            } else {
                $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `offer`='0'";
            }
        } elseif ($type == 1) {
            $jobDetails = $this->isJobSkilled($jobID);
            if ($jobDetails == true) {
                $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `jobType`='Skilled'";
            } else {
                $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `jobType`='Unskilled'";
            }
        } elseif ($type == 4) {
            $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type' and `otherType`='$otherType'";
        } else {
            $query = "SELECT * FROM `feesSetup` WHERE `country`='$country' and `visaType`='$type'";
        }

        // echo $query;
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);

        return $row;
    }
    // minus one from the total jobs.vacancies
    function minusOneJob($jobID)
    {
        $query = "UPDATE `jobs` SET `vacancies` = `vacancies`-1 WHERE `id` = '$jobID'";
        mysqli_query($this->connection, $query);
    }

    function generateVisaFileNumber($visaID)
    {
        $query = "SELECT * FROM `visaApplication` where `id`='$visaID'";
        $result = mysqli_query($this->connection, $query);
        $row = mysqli_fetch_assoc($result);
        $country = $row['applyingFor'];
        $type = $row['type'];
        $offer = $row['offer'];
        $jobID = $row['jobID'];
        $otherType = $row['otherType'];
        $fileNumber = str_pad($visaID, 4, "0", STR_PAD_LEFT);

        // Country codes
        $countryCodes = [
            "AU" => "E",
            "NZ" => "E",
            "Ca" => "N",
            "US" => "V",
            "UK" => "X",
            "Fj" => "B"
        ];

        // Visa codes


        if ($type == 2) {
            if ($offer != "") {
                $visaType = "SO";
            } else {
                $visaType = "SN";
            }
        } elseif ($type == 4) {
            if ($otherType == 1)
                $visaType = "OO";
            if ($otherType == 2)
                $visaType = "OB";
            if ($otherType == 3)
                $visaType = "OI";
            else 
                $visaType = "OM";
            
        } elseif ($type == 1) {
            $jobDetails = $this->isJobSkilled($jobID);
            if ($jobDetails == true) {
                $visaType = "ES";
            } else {
                $visaType = "EU";
            }
        } else {
            $visaType = "V";
        }

        // Convert country & visa to codes
        $countryCode = $countryCodes[$country] ?? "";


        // Date formatting (YY/DD)
        $yearShort = substr(date("Y"), -2);
        $dateFormatted = $yearShort . date("m");




        // Generate Visa File Number
        $visaFileNumber = "{$countryCode}{$visaType}{$dateFormatted}{$fileNumber}";

        // Output result
        return $visaFileNumber;
    }


    function getCountryDD($selected,$name,$disabled=false)
    {
        if($disabled){
            $disabled = "disabled";
        }
        
        $rt = "";
        $rt .= '<select name="'.$name.'" class="form-select ssSelect" id="'.$name.'" '.$disabled.'>';
        $sql = "SELECT * FROM `countries` WHERE 1";
        $result = $this->connection->query($sql);
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // mark selected
                if ($row['iso2'] == $selected) {
                    $rt .= '<option value="' . $row['iso2'] . '" selected>' . $row['name'] . '</option>';
                } else {
                    $rt .= '<option value="' . $row['iso2'] . '">' . $row['name'] . '</option>';
                }
            }
        }

        $rt .= '</select>';

        return $rt;
    }

    function getLanguageDD($selected,$name,$disabled=false,$required=false)
    {
        if($disabled)
            $disabled = "disabled";
        if($required)
            $required = "required";
        $rt = "";
        $rt .= '<select name="'.$name.'" class="form-select ssSelect" '.$disabled.' id="'.$name.'" '.$required.'>';
        $rt .= '<option value="">Select</option>';
        $sql = "SELECT * FROM `languages` WHERE 1";
        $result = $this->connection->query($sql);
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                if ($row['name'] == $selected)
                    $rt .= '<option value="' . $row['name'] . '" selected>' . $row['name'] . '</option>';
                else 
                    $rt .= '<option value="' . $row['name'] . '">' . $row['name'] . '</option>';
            }
        }
        $rt .= '</select>';
        return $rt;
    }
    function getAgentsDD($selected,$name,$disabled=false,$required=false)
    {
        if($disabled)
            $disabled = "disabled";
        if($required)
            $required = "required";
        $rt = "";
        $rt .= '<select name="'.$name.'" class="form-select ssSelect" '.$disabled.' id="'.$name.'" '.$required.'>';
        $rt .= '<option value="">Select</option>';
        $sql = "SELECT * FROM `users` WHERE `type`='3' and `status`='1'";
        $result = $this->connection->query($sql);
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                if ($row['name'] == $selected)
                    $rt .= '<option value="' . $row['name'] . '" selected>' . $row['name'] . '</option>';
                else 
                    $rt .= '<option value="' . $row['name'] . '">' . $row['name'] . '</option>';
            }
        }
        if($selected == "Other")
            $rt .= '<option value="Other" selected>Other</option>';
        else
            $rt .= '<option value="Other">Other</option>';
        $rt .= '</select>';
        return $rt;
    }
    function getAgentsDD2($selected,$name,$id)
    {
        
        $rt = "";
        $rt .= '<select name="'.$name.'" class="form-select ssSelect" id="'.$id.'" required>';
        $rt .= '<option value="">Select</option>';
        $sql = "SELECT * FROM `users` WHERE `type`='3' and `status`='1'";
        $result = $this->connection->query($sql);
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                if ($row['name'] == $selected)
                    $rt .= '<option value="' . $row['name'] . '" selected>' . $row['name'] . '</option>';
                else 
                    $rt .= '<option value="' . $row['name'] . '">' . $row['name'] . '</option>';
            }
        }
        if($selected == "Other")
            $rt .= '<option value="Other" selected>Other</option>';
        else
            $rt .= '<option value="Other">Other</option>';
        $rt .= '</select>';
        return $rt;
    }

    // active jobs on website
    function getActiveJobsDD()
    {
        $rt = "";
        $rt .= '<select class="form-control" id="portalJobs" name="portalJobs" required>';
        $sql = "SELECT * FROM `jobs` WHERE `status`='1'";
        $result = $this->connection->query($sql);
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $rt .= '<option value="' . $row['id'] . '">' . $row['title'] . ' -'.$row['jobType'].' ('.$row['category2'].') </option>';
            }
        }
        $rt .= '</select>';
        return $rt;
    }

    function getLicense($id)
    {
        $query = "SELECT * FROM `agentLicense` where `uid`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
    }
    function getPrimaryPaymentAccount()
    {
        $sql = "SELECT * FROM `accounts`";
        $result = $this->connection->query($sql);
        $row = $result->fetch_assoc();
        return $row['applicationFee'];
    }
    function getAgentRegistrationsFee()
    {
        $sql = "SELECT * FROM `agentRegistrationsFee`";
        $result = $this->connection->query($sql);
        $row = $result->fetch_assoc();
        return $row;
    }
    function getAgentFullDetailsByID($packageID)
    {
        $sql = "SELECT * FROM `agent_packages` where `id`='$packageID'";
        $result = $this->connection->query($sql);
        $row = $result->fetch_assoc();
        return $row;
    }
    function getUserusernameByID($id)
    {
        $query = "SELECT `username` FROM `users` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row['username'];
        }
        return "";
    }
    function encID()
    {
        // 20 digit random phrase
        $phrase = substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, 20);
        return $phrase;
    }   
    function getVisaDetails($id)
    {
        $query = "SELECT * FROM `visaApplication` where `id`='$id'";
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
        return false;
    }

    function getFaq()
    {
        $query = "SELECT * FROM `faq`";
        $result = mysqli_query($this->connection, $query);
        $faqs = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $faqs[] = $row;
        }
        return $faqs;
    }
    function getEOIData($uid)
    {
        $query = "SELECT * FROM `expression_of_interest` WHERE `uid`=" . $uid;
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
        return false;
    }
    function getEOIDataByID($id)
    {
        $query = "SELECT * FROM `expression_of_interest` WHERE `id`=" . $id;
        $result = mysqli_query($this->connection, $query);
        $numResults = mysqli_num_rows($result);
        if ($numResults > 0) {
            $row = mysqli_fetch_assoc($result);
            return $row;
        }
        return false;
    }
    function insertData($table, $data)
    {
        $fields = array_keys($data);
        $values = array_map(array($this->connection, 'real_escape_string'), array_values($data));
        $query = "INSERT INTO $table (" . implode(',', $fields) . ") VALUES ('" . implode("','", $values) . "')";
        // echo $query;
        $result = mysqli_query($this->connection, $query);
        return $result;
    }
}
