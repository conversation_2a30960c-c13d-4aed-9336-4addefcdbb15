<?php
include_once 'includes/config.php';
include 'includes/session.php';
include_once 'includes/generalFunctions.class.php';
$generalFunctions = new generalFunctions($connection);
$title = "Agent Status Check";
if (isset($_COOKIE['userid']) && $_COOKIE['userid'] != "") {
    include_once 'includes/session.php';
    include_once 'includes/header.php';
} else {
    include_once 'includes/header_s.php';
}
$found = false;
// search visa in visaApplication dob is in 3 coulms dobMonth, dobDay, dobYear 
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    if((!isset($_POST['license_num']) || $_POST['license_num'] == "") && (!isset($_POST['l_expiry']) || $_POST['l_expiry'] == "") && (!isset($_POST['full_name']) || $_POST['full_name'] == "") && (!isset($_POST['contact_number']) || $_POST['contact_number'] == "")){
        $message = "<div class='alert alert-danger' role='alert'>Please enter at least one value</div>";
        
    }
    else
    {
    
        $license_num = mysqli_real_escape_string($connection, $_POST['license_num'])? mysqli_real_escape_string($connection, $_POST['license_num']) : '';
        $l_expiry = $generalFunctions->dateToDB(mysqli_real_escape_string($connection, $_POST['l_expiry']))? $generalFunctions->dateToDB(mysqli_real_escape_string($connection, $_POST['l_expiry'])) : '';
        $contact_number = mysqli_real_escape_string($connection, $_POST['contact_number'])? mysqli_real_escape_string($connection, $_POST['contact_number']) : '';
        $full_name = mysqli_real_escape_string($connection, $_POST['full_name'])? mysqli_real_escape_string($connection, $_POST['full_name']) : '';
        
        if($l_expiry != '')
        {
            $l_expiry = "OR l_expiry = '$l_expiry'";
        }
        else
        {
            $l_expiry = "";
        }
        $sql = "SELECT * FROM expression_of_interest WHERE (full_name = '$full_name' $l_expiry OR license_num = '$license_num' OR contact_number = '$contact_number')";
        // echo $sql;
        // exit;
        $result = $connection->query($sql);
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $found = true;
            
            // $message = "<div class='alert alert-success' role='alert'>Your Visa Status is <strong>" .  $generalFunctions->getVisaStatus($row['status']) . "</strong></div>";
        } else {
            $message = "<div class='alert alert-danger' role='alert'>No record found</div>";
        }
    }
} 
?>
<main>
    <section class="py-2 bg-light">
        <div class="container">
            <div class="row d-flex align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-2 fs-2">Agent Status Check</h1>
                </div>
            </div>
        </div>
    </section>
    <section class="py-5">
        <div class="container">
            <div class="row d-flex align-items-center">
                <div class="col-md-12">
                    <div>
                        <div class="card-body">
                            <?php echo isset($message) ? $message : ''; ?>
                            <form method="post" action="" id="dataForm" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="search">License Number:</label>
                                            <input type="text" class="form-control" id="license_num" value="<?php echo isset($license_num)?$license_num:''; ?>" name="license_num" placeholder="Enter License Number">
                                            <div class="invalid-feedback">Please enter a value</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="search">License Expiry:</label>
                                            <input type="date" class="form-control" id="l_expiry" value="<?php echo isset($l_expiry)?$l_expiry:''; ?>" name="l_expiry" placeholder="Enter License Expiry">
                                            <div class="invalid-feedback">Please enter a value</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="search">Full Name:</label>
                                            <input type="text" class="form-control" id="full_name" value="<?php echo isset($full_name)?$full_name:''; ?>" name="full_name" placeholder="Enter Full Name">
                                            <div class="invalid-feedback">Please enter a value</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="search">Contact Number:</label>
                                            <input type="text" class="form-control" id="contact_number" value="<?php echo isset($contact_number)?$contact_number:''; ?>" name="contact_number" placeholder="Enter Contact Number">
                                            <div class="invalid-feedback">Please enter a value</div>
                                        </div>
                                    </div>
                                    
                                    
                                    <div class="col-md-12">
                                        <button type="submit" class="btn btn-primary" id="btnSubmit">Search</button>
                                    </div>
                                </div>
                            </form>
                            <?php if($found){ ?>
                            <div class="table-responsive mt-3">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>License Number</th>
                                            <th>License Expiry</th>
                                            <th>Full Name</th>
                                            <th>Contact Number</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        <?php
                                        $user = $generalFunctions->getUserByID($row['uid']);
                                            if($user['status'] == 1)
                                            $status = "Active";
                                        else if($user['status'] == 2)
                                            $status = "Inactive";
                                        else
                                            $status = "Suspended";

                                            
                                            echo "<tr>";
                                            echo "<td>" . $row['license_num'] . "</td>";
                                            echo "<td>" . $generalFunctions->dateToRead($row['l_expiry']) . "</td>";
                                            echo "<td><a href='#' class='agent-name-link text-primary' data-agent-id='" . $row['id'] . "'>" . $row['full_name'] . "</a></td>";
                                            echo "<td>" . $row['contact_number'] . "</td>";
                                            echo "<td>" . $status . "</td>";
                                            echo "</tr>";
                                        
                                        ?>

                                    </tbody>
                                </table>
                            </div>
                            <?php } ?>

                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
</main>

<!-- Agent Information Modal -->
<div class="modal fade" id="agentInfoModal" tabindex="-1" aria-labelledby="agentInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="agentInfoModalLabel">
                    <i class="bi bi-person-badge"></i> Agent Information
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="agentInfoContent">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading agent information...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php include("includes/footer.php") ?>
<link href="https://unpkg.com/gijgo@1.9.14/css/gijgo.min.css" rel="stylesheet" type="text/css" />
<!-- fontawsome csss-->

<style>
.agent-name-link {
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.agent-name-link:hover {
    text-decoration: underline;
    color: #0056b3 !important;
}

.modal-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
}

.agent-photo-container {
    position: relative;
    display: inline-block;
}

.agent-photo-container::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, #0d6efd, #20c997, #fd7e14, #dc3545);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.info-item strong {
    color: #495057;
    font-size: 0.95rem;
}

.info-item span {
    color: #6c757d;
}

.badge {
    font-size: 0.8rem;
    padding: 0.5em 0.75em;
}
</style>


<script src="https://unpkg.com/gijgo@1.9.14/js/gijgo.min.js" type="text/javascript"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script>
    $(function() {
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });

        // Handle agent name click to show popup
        $(document).on('click', '.agent-name-link', function(e) {
            e.preventDefault();
            var agentId = $(this).data('agent-id');

            // Show modal
            $('#agentInfoModal').modal('show');

            // Reset modal content to loading state
            $('#agentInfoContent').html(`
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading agent information...</p>
                </div>
            `);

            // Fetch agent details via AJAX
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                dataType: 'html',
                data: {
                    action: 'getAgentInfo',
                    id: agentId
                },
                success: function(response) {
                    $('#agentInfoContent').html(response);
                },
                error: function() {
                    $('#agentInfoContent').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            Error loading agent information. Please try again.
                        </div>
                    `);
                }
            });
        });

        $('#dobDate').datepicker({
        uiLibrary: 'bootstrap4',
        iconsLibrary: 'fontawesome',
        format: 'yyyy-mm-dd',
        showRightIcon: false,
        maxDate: function() {
            maxDate = new Date();
            return maxDate;
        }
    });
    });
</script>
</body>

</html>