<?php 
include('includes/config.php');
include('includes/session.php');
include("includes/generalFunctions.class.php");
include('paypal.php');
$general = new generalFunctions($connection);

use PayPal\Api\Payment;
use PayPal\Api\PaymentExecution;

// Assuming paymentId and payerId are available from the PayPal return URL
if (isset($_GET['paymentId']) && isset($_GET['PayerID'])) {
    $paymentId = $_GET['paymentId'];
    $payerId = $_GET['PayerID'];
    $id = mysqli_real_escape_string($connection, $_GET['uid']);

    // Get the payment object
    $payment = Payment::get($paymentId, $apiContext);

    // Execute the payment
    $execution = new PaymentExecution();
    $execution->setPayerId($payerId);

    try {
        // Execute the payment
        $result = $payment->execute($execution, $apiContext);
        // echo "<pre>";
        // echo $result->getState();
        // print_r($result);
        // exit;

        // Check payment status
        // check if visa application status is alredy 1
        foreach($_SESSION['multiApplication'] as $key => $value)
        {
            $id = $value;
            $sql = "SELECT * FROM `visaApplication` WHERE `id` = '$id'";
            $resultAlready = mysqli_query($connection, $sql);
            $row = mysqli_fetch_assoc($resultAlready);
            if($row['status'] == 1)
            {
                $_SESSION['msg'] = "The payment has been already received.";
                $_SESSION['heading'] = "Payment Received";
                $_SESSION['type'] = "success";
                continue;
                // echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
                // exit;
            }
            if ($result->getState() == 'approved') {
                if($row['type'] == 1)
                {
                    $jobID = $row['jobID'];
                    $general->minusOneJob($jobID);
                }
                $TRN = $general->generateVisaFileNumber($id); // generate TRN
                $receivedAmount = ($payment->transactions[0]->amount->total/count($_SESSION['multiApplication']));
                $transaction_id = $result->getId();
                
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                                VALUES 
                                                ('".userid."', '$transaction_id', 'Visa Application Fee - {$TRN}', '".$receivedAmount."', 'credit', 'paypal', 1, '$id')";
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);
                
                
                
                $sql = "UPDATE `visaApplication` SET `status` = 1,TRN='$TRN' WHERE `id` = '$id'";
                mysqli_query($connection, $sql);
                if($row['type'] == 1)
                {
                    $visaType = "Work Visa";
                }
                elseif($row['type'] == 2)
                {
                    $visaType = "Student Visa";
                }
                else
                {
                    $visaType = "Visitor Visa";
                }
                $subject = "Visa Application Received - Confirmation";
                $bodyMail = 'Dear '.$row['lastName'].',<br><br>We have successfully received your visa application. Our team is currently reviewing your submission, and we will update you on the next steps soon.<br><br>Application Details:<br><br>Applicant Name: <b>'.$row['lastName'].'</b><br>Visa Type: <b>'.$visaType.'</b><br>Application Number: <b>'.$TRN.'</b><br>Application Date: <b>'.$general->dateToRead($row['created']).'</b>';
                // $bodyMail add payment received receipt
                $bodyMail .= '<br><br>Payment Details:<br><br>Amount: <b>'.$receivedAmount.'</b><br>Transaction ID: <b>'.$transaction_id.'</b><br>Payment Date: <b>'.$general->dateToRead(date('Y-m-d H:i:s')).'</b>';
                $bodyMail .= '<br><br>If any additional information is required, we will reach out to you. For inquiries, feel free to contact our support <NAME_EMAIL>.<br><br>Thank you for choosing '.sitename;
                $general->sendEmail($row['lastName'], $row['email'], $subject, $bodyMail);

                // FCO Accounts Payment
                
                $feesAmount     = $general->getFeeByVisaApplicationID($id);
                $applicationFee = $feesAmount['applicationFee'];
                $fcoLevy        = $feesAmount['fcoLevy'];
                $commission     = $feesAmount['commission'];
                $webFee         = $feesAmount['webFee'];

                $sql = "SELECT * FROM `accounts`";
                $result2 = mysqli_query($connection, $sql);
                $row = mysqli_fetch_assoc($result2);
                $applictionAccount  = $row['applicationFee'];
                $fcoLevyAccount     = $row['fcoLevyFee'];
                $commissionAccount  = $row['comissionFee'];
                $webFeeAccount      = $row['webFee'];

                // `applicationFee`, `fcoLevyFee`, `comissionFee`
                
                $transaction_id = payoutToFCOAccounts($webFeeAccount, $webFee, "Web fee paid");
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                                VALUES 
                                                ('-1', '$transaction_id', 'Web fee (".$webFeeAccount.")', '".$webFee."', 'debit', 'paypal', 1, '$id')";
                
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);

                $transaction_id = payoutToFCOAccounts($fcoLevyAccount, $fcoLevy, "FCO levy fee paid");
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                                VALUES 
                                                ('-1', '$transaction_id', 'FCO levy fee (".$fcoLevyAccount.")', '".$fcoLevy."', 'debit', 'paypal', 1, '$id')";
                
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);

                $transaction_id = payoutToFCOAccounts($commissionAccount, $commission, "Commission paid");
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                                VALUES 
                                                ('-1', '$transaction_id', 'Commission fee (".$commissionAccount.")', '".$commission."', 'debit', 'paypal', 1, '$id')";
                
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);
                // $_SESSION['msg'] = "The payment has been successfully received.";
                // $_SESSION['heading'] = "Payment Received";
                // $_SESSION['type'] = "success";
                // echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
                // exit;



            }
            elseif($result->getState() == 'pending') 
            {
                $sql = "INSERT INTO transactions (`uid`, `transaction_id`, `description`, `amount`, `type`, `method`, `status`, `item_id`) 
                                                VALUES 
                                                ('".userid."', '$transaction_id', 'Visa Application Fee', '".$receivedAmount."', 'credit', 'paypal', 0, '$id')";
                mysqli_query($connection, $sql);
                $payment_id = mysqli_insert_id($connection);

                // payment pending
                $_SESSION['msg'] = "The payment is currently pending.";
                $_SESSION['heading'] = "Payment Pending";
                $_SESSION['type'] = "warning"; 
                // echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
                // exit;
            }
            else {

                $_SESSION['msg'] = "Payment has been failed.";
                $_SESSION['heading'] = "Payment Failed";
                $_SESSION['type'] = "error";
                // echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
                // exit;
                
            }   
        }
        $_SESSION['msg'] = "The payment has been successfully received.";
        $_SESSION['heading'] = "Payment Received";
        $_SESSION['type'] = "success";
        echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
        exit; 

    } catch (Exception $ex) {
        $_SESSION['msg'] = "Payment has been failed.";
        $_SESSION['heading'] = "Payment Failed";
        $_SESSION['type'] = "error";
        echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
        exit;
    }
} else {

    $_SESSION['msg'] = "Payment has been failed.";
    $_SESSION['heading'] = "Payment Failed";
    $_SESSION['type'] = "error";
    echo "<meta http-equiv='refresh' content='0;url=notify.php'>";
    exit;
}

?>