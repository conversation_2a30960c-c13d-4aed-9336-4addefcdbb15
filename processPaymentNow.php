<?php

include('includes/config.php');
// include('includes/session.php');
include("includes/generalFunctions.class.php");
$generalFunctions = new generalFunctions($connection);

// header("Location: processPaymentNow.php?encID=$encID&paymentID=$paymentID");

if(isset($_GET['encID']) && isset($_GET['paymentID'])) {
    $encID = mysqli_real_escape_string($connection, $_GET['encID']);
    $paymentID = mysqli_real_escape_string($connection, $_GET['paymentID']);
    
    
    $query = "SELECT * FROM tempPament WHERE id = '$paymentID' AND encID = '$encID'";
    $result = mysqli_query($connection, $query);
    
    if(mysqli_num_rows($result) > 0) {
        $tempPament = mysqli_fetch_assoc($result);
        $amount = $tempPament['amount'];
        $encID = $tempPament['encID'];
        
        
    } else {
        // Payment does not exist, redirect to an error page or show a message
        header("Location: dashboard.php");
        exit;
    }
} 
else {
    // Redirect to an error page or show a message
    header("Location: dashboard.php");
    exit;
}

$title = "Payment Now";
if (isset($_COOKIE['userid']) && $_COOKIE['userid'] != "") {
    include_once 'includes/session.php';
    include_once 'includes/header.php';
} else {
    include_once 'includes/header_s.php';
}

?>
<style>
    ._failed {
        border-bottom: solid 4px red !important;
    }

    ._failed i {
        color: red !important;
    }

    ._warning {
        border-bottom: solid 4px orange !important;
    }

    ._warning i {
        color: orange !important;
    }

    ._success {
        box-shadow: 0 15px 25px #00000019;
        padding: 45px;
        width: 100%;
        text-align: center;
        margin: 40px auto;
        border-bottom: solid 4px #28a745;
    }

    ._success i {
        font-size: 55px;
        color: #28a745;
    }

    ._success h2 {
        margin-bottom: 12px;
        font-size: 40px;
        font-weight: 500;
        line-height: 1.2;
        margin-top: 10px;
    }

    ._success p {
        margin-bottom: 0px;
        font-size: 18px;
        color: #495057;
        font-weight: 500;
    }
</style>
<main>
    <section class="py-2 bg-light">
        <div class="container">
            <div class="row d-flex align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-2 fs-2">Payment Now</h1>
                </div>
            </div>
        </div>
    </section>
    <!-- section close -->
    <!--  counsulting start -->
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <p class="text-center">Click the button below to make a payment.</p>
                        <div id="paypal-button-container"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

 
<?php
include("includes/footer.php");
?>
<script src="https://www.paypal.com/sdk/js?client-id=<?php echo PAYPALCLIENTID; ?>&currency=USD&debug=false&disable-funding=venmo,sepa,paylater"></script>
<script>
        paypal.Buttons({
            createOrder: async function(data, actions) {
                try {
                    // Fetch dynamic order data from your server
                    const response = await fetch('ajaxLoad.php?action=getApplicationPayment&encID=<?php echo $encID; ?>&paymentID=<?php echo $paymentID; ?>', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    const orderData = await response.json();

                    if (!orderData || !orderData.amount || orderData.amount <= 0) {
                        alert('Invalid order data from server.');
                        return false;
                    }

                    
                    // Create PayPal order
                    return actions.order.create({
                        purchase_units: [{
                            reference_id: orderData.order_id,
                            description: 'FCO Visa Payment',
                            amount: {
                                value: orderData.amount
                            }
                        }]
                    });

                } catch (error) {
                    console.error('Error fetching dynamic data:', error);
                    alert('Failed to load order data. Try again.');
                    return false;
                }
            },
            onApprove: async function(data, actions) {
                try {
                    const order = await actions.order.capture();
                    window.location.href = 'processPaymentReceived.php?orderID=' + order.id;
                } catch (err) {
                    // alert('Payment failed. Please try again.\n\nError: ' + err);
                    actions.close(); // Close popup on error
                }
            },
            onError: function(err) {
                // alert('Payment failed. Please try again.\n\nError: ' + err);
            }
        }).render('#paypal-button-container');
    </script>
</body>

</html>